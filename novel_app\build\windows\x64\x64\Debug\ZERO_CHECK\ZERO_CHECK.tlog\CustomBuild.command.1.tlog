^D:\PROJECT\VS CODE\NOVEL_APP002\NOVEL_APP\BUILD\WINDOWS\X64\CMAKEFILES\9CAF15A5B5A9A7F7685D44ADF113001B\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/project/vs code/novel_app002/novel_app/windows" "-BD:/project/vs code/novel_app002/novel_app/build/windows/x64" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "D:/project/vs code/novel_app002/novel_app/build/windows/x64/novel_app.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
