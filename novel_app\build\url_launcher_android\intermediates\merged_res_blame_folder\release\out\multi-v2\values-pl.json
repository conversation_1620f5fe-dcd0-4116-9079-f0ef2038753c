{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-merged_res-26:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\79149aa32c9e18ffde263c4b65b44a39\\transformed\\browser-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "792,892,991,1106", "endColumns": "99,98,114,103", "endOffsets": "887,986,1101,1205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\3025b161e7fedcc5fe36d08ffbecc89c\\transformed\\core-1.13.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,1210", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,1306"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-24:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\79149aa32c9e18ffde263c4b65b44a39\\transformed\\browser-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "792,892,991,1106", "endColumns": "99,98,114,103", "endOffsets": "887,986,1101,1205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\3025b161e7fedcc5fe36d08ffbecc89c\\transformed\\core-1.13.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,1210", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,1306"}}]}]}