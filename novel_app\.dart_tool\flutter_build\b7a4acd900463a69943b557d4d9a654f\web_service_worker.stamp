{"inputs": ["build\\web\\assets\\AssetManifest.bin", "build\\web\\assets\\AssetManifest.bin.json", "build\\web\\assets\\AssetManifest.json", "build\\web\\assets\\assets\\fonts\\NotoSerifSC-Regular.otf", "build\\web\\assets\\assets\\images\\coffee_qrcode.svg", "build\\web\\assets\\assets\\images\\wechat_pay.png.jpg", "build\\web\\assets\\assets\\version.json", "build\\web\\assets\\FontManifest.json", "build\\web\\assets\\fonts\\MaterialIcons-Regular.otf", "build\\web\\assets\\NOTICES", "build\\web\\assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "build\\web\\assets\\shaders\\ink_sparkle.frag", "build\\web\\build\\web\\assets\\AssetManifest.bin.json", "build\\web\\build\\web\\assets\\FontManifest.json", "build\\web\\build\\web\\icons\\Icon-192.png", "build\\web\\canvaskit\\canvaskit.js", "build\\web\\canvaskit\\canvaskit.js.symbols", "build\\web\\canvaskit\\canvaskit.wasm", "build\\web\\canvaskit\\chromium\\canvaskit.js", "build\\web\\canvaskit\\chromium\\canvaskit.js.symbols", "build\\web\\canvaskit\\chromium\\canvaskit.wasm", "build\\web\\canvaskit\\skwasm.js", "build\\web\\canvaskit\\skwasm.js.symbols", "build\\web\\canvaskit\\skwasm.wasm", "build\\web\\canvaskit\\skwasm_st.js", "build\\web\\canvaskit\\skwasm_st.js.symbols", "build\\web\\canvaskit\\skwasm_st.wasm", "build\\web\\DEPLOYMENT.md", "build\\web\\download_canvaskit.py", "build\\web\\favicon.png", "build\\web\\flutter.js", "build\\web\\flutter_backup.js", "build\\web\\flutter_bootstrap.js", "build\\web\\flutter_canvaskit_config.js", "build\\web\\icons\\Icon-192.png", "build\\web\\icons\\Icon-512.png", "build\\web\\icons\\Icon-maskable-192.png", "build\\web\\icons\\Icon-maskable-512.png", "build\\web\\index.html", "build\\web\\index.html.new", "build\\web\\main.dart.js", "build\\web\\manifest.json", "build\\web\\nginx.conf.example", "build\\web\\test.html", "build\\web\\version.json", "build\\web\\web.config", "build\\web\\welcome.html", "build\\web\\welcome_simple.html"], "outputs": ["build\\web\\flutter_service_worker.js"]}