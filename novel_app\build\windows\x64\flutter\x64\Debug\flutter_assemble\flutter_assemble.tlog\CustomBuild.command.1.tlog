^D:\PROJECT\VS CODE\NOVEL_APP002\NOVEL_APP\BUILD\WINDOWS\X64\CMAKEFILES\CC3A08DFEBA2733BE7C10DA09BB1B742\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\element\flutter "PROJECT_DIR=D:\project\vs code\novel_app002\novel_app" FLUTTER_ROOT=D:\element\flutter "FLUTTER_EPHEMERAL_DIR=D:\project\vs code\novel_app002\novel_app\windows\flutter\ephemeral" "PROJECT_DIR=D:\project\vs code\novel_app002\novel_app" "FLUTTER_TARGET=D:\project\vs code\novel_app002\novel_app\lib\main.dart" DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false "PACKAGE_CONFIG=D:\project\vs code\novel_app002\novel_app\.dart_tool\package_config.json" D:/element/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\PROJECT\VS CODE\NOVEL_APP002\NOVEL_APP\BUILD\WINDOWS\X64\CMAKEFILES\1DB4D80A84EFF27DC224A69E957E1A2E\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\PROJECT\VS CODE\NOVEL_APP002\NOVEL_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SD:/project/vs code/novel_app002/novel_app/windows" "-BD:/project/vs code/novel_app002/novel_app/build/windows/x64" --check-stamp-file "D:/project/vs code/novel_app002/novel_app/build/windows/x64/flutter/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
