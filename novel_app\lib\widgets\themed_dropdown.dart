import 'package:flutter/material.dart';

/// 带有主题的下拉按钮组件
/// 确保在所有主题下都有正确的背景色
class ThemedDropdownButton<T> extends StatelessWidget {
  final T? value;
  final String? hint;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final Widget? icon;
  final Color? iconEnabledColor;
  final Widget? underline;
  final bool isExpanded;
  final double? itemHeight;
  final FocusNode? focusNode;
  final bool autofocus;
  final Color? dropdownColor;

  const ThemedDropdownButton({
    super.key,
    this.value,
    this.hint,
    required this.items,
    this.onChanged,
    this.icon,
    this.iconEnabledColor,
    this.underline,
    this.isExpanded = false,
    this.itemHeight,
    this.focusNode,
    this.autofocus = false,
    this.dropdownColor,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return DropdownButton<T>(
      value: value,
      hint: hint != null ? Text(hint!) : null,
      items: items,
      onChanged: onChanged,
      icon: icon,
      iconEnabledColor: iconEnabledColor,
      underline: underline,
      isExpanded: isExpanded,
      itemHeight: itemHeight,
      focusNode: focusNode,
      autofocus: autofocus,
      dropdownColor: dropdownColor ?? 
          (isDark ? const Color(0xFF2A2A2A) : Colors.white),
    );
  }
}

/// 带有主题的下拉按钮表单字段组件
class ThemedDropdownButtonFormField<T> extends StatelessWidget {
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final InputDecoration? decoration;
  final FormFieldValidator<T>? validator;
  final FormFieldSetter<T>? onSaved;
  final Widget? icon;
  final Color? iconEnabledColor;
  final bool isExpanded;
  final double? itemHeight;
  final FocusNode? focusNode;
  final bool autofocus;
  final Color? dropdownColor;

  const ThemedDropdownButtonFormField({
    super.key,
    this.value,
    required this.items,
    this.onChanged,
    this.decoration,
    this.validator,
    this.onSaved,
    this.icon,
    this.iconEnabledColor,
    this.isExpanded = false,
    this.itemHeight,
    this.focusNode,
    this.autofocus = false,
    this.dropdownColor,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: onChanged,
      decoration: decoration,
      validator: validator,
      onSaved: onSaved,
      icon: icon,
      iconEnabledColor: iconEnabledColor,
      isExpanded: isExpanded,
      itemHeight: itemHeight,
      focusNode: focusNode,
      autofocus: autofocus,
      dropdownColor: dropdownColor ?? 
          (isDark ? const Color(0xFF2A2A2A) : Colors.white),
    );
  }
}
