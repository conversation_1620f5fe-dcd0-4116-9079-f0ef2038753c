﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\x64\Debug\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\flutter\x64\Debug\flutter_assemble</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\file_selector_windows\Debug\file_selector_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\just_audio_windows\Debug\just_audio_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\permission_handler_windows\Debug\permission_handler_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\share_plus\Debug\share_plus_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\plugins\url_launcher_windows\Debug\url_launcher_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\runner\Debug\novel_app.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\x64\Debug\ALL_BUILD</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\project\vs code\novel_app002\novel_app\build\windows\x64\x64\Debug\INSTALL</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>