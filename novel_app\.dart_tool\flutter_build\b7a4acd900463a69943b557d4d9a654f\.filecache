{"version": 2, "files": [{"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_instance\\src\\get_instance.dart", "hash": "e9f67c2f22dba094a904897de6d5e823"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\controllers\\tts_controller.dart", "hash": "5e230b9649f22dab426e0236c3db2f50"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\escape_syntax.dart", "hash": "db883acf8377f30b63838a7c665d4c20"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_enum.dart", "hash": "4817a73df1c313cf6a6eb86774e7fc99"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_transformer\\typed.dart", "hash": "35c9371cbb421753e99a2ca329107309"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart", "hash": "8141617d0d061a10cb35d7c324354241"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\card_theme.dart", "hash": "d65982353903b8d6b3d8697fa43abc7b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\number_symbols_data.dart", "hash": "607a9180d57d1885fbf3b44ac080f162"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\_web_image_info_web.dart", "hash": "601316e5dc9ec49aaa038e4af5649490"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart", "hash": "92f95df2d036abce6b76bc19cba36301"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_daemon-4.0.4\\LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart", "hash": "eaa572d06b6edfa39d1b51f873c658be"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\expression.dart", "hash": "79503c7448238b77502c169788e26dbf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\secure_payment_confirmation.dart", "hash": "ff010ada1c7b3a396c3bb39b067fb53d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart", "hash": "a05b35e402965727a6b38bfc20b6ea15"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\chat_models\\chat_models.dart", "hash": "d77c7dafddb482a50609c8db1929ba8a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\runnable\\binding.dart", "hash": "b5216156ea7a0cb4cedee218320946cb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\headers.dart", "hash": "92b69c1c8a23d03727271c02850b8fd9"}, {"path": "build\\web\\canvaskit\\skwasm.js.symbols", "hash": "80806576fa1056b43dd6d0b445b4b6f7"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\test.html", "hash": "5d9a9a682e70dec085a9245673dc57e2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\prompts\\chat_prompt.dart", "hash": "57f0130cf30bec6489ad16fbf543a98c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\lib\\url_launcher_linux.dart", "hash": "9d67bda83980287cc1100fe7fad9e05d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\restartable_timer.dart", "hash": "89cdb68e09dda63e2a16d00b994387c2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\interface\\interface.dart", "hash": "6a657c7d596dd0fc1e717f10dd6786ac"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\box_fit.dart", "hash": "********************************"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\physics\\utils.dart", "hash": "670717573525d5e0518a4063c6fd9231"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart", "hash": "baa950789be2218b22365b1c67b8d6f3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\list_models_response.dart", "hash": "b2198a12ed3251d911aa2e9060ed0753"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\map_not_null.dart", "hash": "929b5628541e8ab826e753c9fe90cd30"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\document.dart", "hash": "6c2432dadd034634eca5a3321f519f58"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart", "hash": "76d81136ee4cb4ea05328b7a8db8b885"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\lib\\src\\types\\html.dart", "hash": "ca830189d7aafefe756316844e568c2e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\models\\models.dart", "hash": "d5ed6fce7a8e6fb197968e276f15de1f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\negatable.dart", "hash": "c188501857ba1fe7f4c808cdd12e9ea2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_completer.dart", "hash": "b9531c458d313a022930a0842db8201e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\utils\\failure_joiner.dart", "hash": "322037160bbd0d8f16bd4e77abfc61f4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\method_channel\\method_channel_file_selector.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\regexp.dart", "hash": "10ca1bc893fd799f18a91afb7640ec26"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_workers\\utils\\debouncer.dart", "hash": "f4b2d5cbcc7333a63cb94675dd1833bd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\fenced_blockquote_syntax.dart", "hash": "d8b54f380b6b2d3ad1441599c8cec76e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\replay_stream.dart", "hash": "c86f575dce7f62595d9f1e169492f750"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\scan.dart", "hash": "352139677d0d5e7dbf8941093403250b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart", "hash": "5c63d2e99643f92534b54d58dbac13b0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\router_report.dart", "hash": "a4338378d8ab082267f0cadc1f82e473"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\knowledge_document.dart", "hash": "728069750ba1086a78d71c0662cb4656"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "build\\web\\index.html.new", "hash": "7c20167368b8f1b6ddbfc83dac676c27"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\utils\\utils.dart", "hash": "dd96c5f91796e0032005b545e32001b3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart", "hash": "b43d65dcf4e6f76d5ad48381b3ea60ee"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar.dart", "hash": "164146a96eb3ced8381be57fbad8ca63"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart", "hash": "59ba7bdfc57111a2c59ae46a61e0daa1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_header.dart", "hash": "4082637928b7b78efd6c2bf895818e6e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\micro_money.dart", "hash": "391b7eda9bffdd4386292eae157d449c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\fun\\standard\\value.dart", "hash": "a86be863f829fd61aa491539b046b602"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\obb3.dart", "hash": "f7fd689f4549dd97ac670c72e4d617c6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\selectable_text.dart", "hash": "3cecc79e9f49ae455b0e499170c2212a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\delimiter_syntax.dart", "hash": "c9399a05e29c2f8b47a7713f8cefdee2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\lib\\cross_file.dart", "hash": "b5c8f4dba868efb80ed69fcd5a7d3f07"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\sink.dart", "hash": "87e6007f2e4468fd84513f05cafcca2d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_cascade.dart", "hash": "e3f89d472d6e772b82c5e22a6a8fc60d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart", "hash": "d13b021628351bce6b686e67998d5eb3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\interface\\tags.dart", "hash": "1b99238f10c84cafca6c5785cd825531"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\notification.dart", "hash": "b9609815ffdb79eb76f2cd3a77a60906"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\readable_stream\\readable_stream_default_reader_chunk.dart", "hash": "fc4d4f23fe13d0213325ead2458acfd9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\unbounded.dart", "hash": "a617a91b12a3156406da1d95552aa4a0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\adapter.dart", "hash": "ffeb03ba26ab6fd8a8c92231f4a64bec"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\linear_border.dart", "hash": "834ed7d1da7d9cd0a0af98bb92ee6231"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\utils.dart", "hash": "6af3329ecded0d459196f5cab2bff744"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\khr_parallel_shader_compile.dart", "hash": "4b5e75750af9287906939a58af8510de"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\charcode.dart", "hash": "089de1957ddfa3cc85f7d791e7438551"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_define_property\\js_string_or_js_symbol.dart", "hash": "1144dc97874f67ab161a42a59e76d1b7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\text_span.dart", "hash": "d0e9c96ae4b02ee7bb4fc08d2a4e0e59"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\blockquote_syntax.dart", "hash": "b71b9ab4f7f758a117b48268efba51fc"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\image.dart", "hash": "b06018282363c4cfcf6d9693d15b29a2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\tokenizer.dart", "hash": "80b8464423b79136f9fc5a427a1dafb4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_define_property\\js_property_descriptor.dart", "hash": "8a02cd5e2ee4727b9ad04d8d0b035451"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\using.dart", "hash": "f70fdb6ec3125a8d6f6fb5ea4cbac59a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\service_status.dart", "hash": "5072fb1450640d8f46605ff67dafa147"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\response\\response.dart", "hash": "9fac58f0f87fa608b73e7f39171e7abe"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\stepper.dart", "hash": "3f622dcf159cb09069184bd32f4b3779"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\footnote_ref_syntax.dart", "hash": "9bcd206d522ed3cfbcb1606458972442"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\response\\response_body.dart", "hash": "79dfc0a1b078087465298ee7154c893d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\just_audio_web-0.4.15\\LICENSE", "hash": "fba1d88f08129f8046dd091b8abb5ded"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\web_animations_2.dart", "hash": "22b72e70978c2bbfb3b0c370a22b9282"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\console.dart", "hash": "54b083c045385cbe9db78b82c60a4d93"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\debug.dart", "hash": "977f3d8dcf7779ef53ea1468a8779d5a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\setext_header_with_id_syntax.dart", "hash": "7204c02a0898c1910830fa5cc63b2519"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart", "hash": "513c9bbd4db002691fa2f6639a0bc3d4"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\manifest.json", "hash": "f448fbac2eb30773e5cabf3d01392d1c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request\\request_cache.dart", "hash": "39ab8beae1fe2be097b790c9786e6257"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\event_sink.dart", "hash": "acfd72852e16d10d8797be366c796133"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\expression\\nodes.dart", "hash": "ece477a2b81de752bc9d5414c58ca997"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\autocomplete.dart", "hash": "d5984a3e5c1a690261b063c6fcfc244d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\letter.dart", "hash": "35ae3adcf5e51919e36509ef828107a7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\simple\\mixin_state.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\date_symbols.dart", "hash": "4c94c1ae460dd53255786f0ce3b53463"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\method_channel\\utils\\codec.dart", "hash": "020552519d966b598cd3bb17849a3a49"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\simple\\simple_builder.dart", "hash": "06cef158bfa7c5f19bf2f789283ded88"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart", "hash": "697b345e3b00008c024c87f80dc0b87f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\resampler.dart", "hash": "d714b72a4f8f2b38a6144a6d8bfbfd74"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart", "hash": "7c075f560ba41b48b8925fa3adf02f3b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\maybe_just_nothing-0.5.3\\lib\\src\\merger.dart", "hash": "528f0d3878e173dd9bf95bfd3c4ca1a0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_group.dart", "hash": "d16df8af6c029bc5e12bedcb2d9ed464"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-4.2.0\\lib\\src\\package_info_plus_linux.dart", "hash": "207e48c523ce7058981a5a5c33da147a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\src\\url_launcher_platform.dart", "hash": "0321281951240b7522f9b85dc24cb938"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart", "hash": "77d5375cf90d1bd09021ac29f4ca8a1d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart", "hash": "de48d59f2b8a22c29dd37492fb4443ec"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart", "hash": "6f4d72d38afe16a9eeb2d21bc14e2460"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\storage\\encoder_backed.dart", "hash": "0dd7a6af9d8ad603f4f3db24a8c41b6a"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\announcement_screen.dart", "hash": "3f22666c632c859e4e0aa9600eeac7e3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\group_by.dart", "hash": "6c3232594edbc47bd6ec36d04c194a9a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\scheduler\\priority.dart", "hash": "9051680cd2078f92c9c56831272643d5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\delay.dart", "hash": "46133866c09984f60ac2731cf9094a27"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\backend\\js\\backend_manager.dart", "hash": "2c5b2fbea5ee050c67c19b11412fd9d9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_texture_compression_rgtc.dart", "hash": "541fce8c5326dac6975fa2876b00a710"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\assets\\images\\coffee_qrcode.svg", "hash": "ff699580c3a753bd0b6b043d50888258"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\posix-6.0.2\\LICENSE", "hash": "2a68e6b288e18606a93b3adf27dbf048"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\simple\\get_controllers.dart", "hash": "3dbf47aa8b7376ede2b1f62e1feeabd5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_storage-2.1.1\\lib\\src\\storage\\html.dart", "hash": "800221ab72737dead3b8b4b54ed8723f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart", "hash": "eb0361b270543f45fe1841c22539289f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\message_codecs.dart", "hash": "7a804325c8972a8d7a59f08a66fb672c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\csp.dart", "hash": "a91a10d47bd8bc0b0647fbfb09173dd9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart", "hash": "bfcabe2e238e6fea7e1140e1cd4ad4eb"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\donate_screen.dart", "hash": "9472502ee3826876570bf3639e1f79f2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_function_call_option.dart", "hash": "9a1c7efdf3bc0033c4730ed0e29d15bb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request\\request_credentials.dart", "hash": "821784ce546a5bbe13908fbb4599b479"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\do.dart", "hash": "96594345ee8d89e2fd23dbca09121153"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\chat_models\\models\\models.dart", "hash": "166dcfaa871e8bf83c99eb0f7cc4121b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\oes_texture_half_float_linear.dart", "hash": "8e5a3b57694eb6cde651f6cc2cb72fef"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_fine_tuning_job_request.dart", "hash": "7397cb0e804458b61586e2d1f4c951dc"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart", "hash": "118be27ff90e0836323d9c71078cb122"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\exhaust_map.dart", "hash": "4c61dffec4ef48c5b09f3009e7765657"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\vector_store.dart", "hash": "ef81d1f9bda8149a4d65f3a81a22f7b1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\snackbar\\snackbar.dart", "hash": "fe73047b3187585cf164eec68bd69f25"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart", "hash": "f25ad5ad3704aecbea8e55cdf889b1b0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webvtt.dart", "hash": "a50e79e8234b2f6a058726e5a910ffb3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\widget_extensions.dart", "hash": "38b66c17195981b78203c7f79fd7e353"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webrtc_priority.dart", "hash": "4a6d26f0dbca3a5a449047a11471ac54"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\not.dart", "hash": "d4acdced936c4825eed27ed61fc28660"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\enhanced_outline_import_service.dart", "hash": "00dcb607172c1dc3c90350ac68902c13"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart", "hash": "23b4272b5eb55e3cf52556499d92ecad"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\services.dart", "hash": "7810a57e5779ecb9d8846373a98333f7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\noise.dart", "hash": "e9fe7ebb2a16174d28ca146824370cec"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_define_property\\js_accessor_descriptor.dart", "hash": "e58f92a4b1179cfb6b4655bb2fb8dfd8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart", "hash": "c33e24b9037c1e3435006428541f2685"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\code_builder-4.10.1\\LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\wrap.dart", "hash": "e0e8407f511d45af90555246b991c170"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\json_path_parser.dart", "hash": "f78713401570cea6fd540390f2bdafbf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\csv_settings_autodetection.dart", "hash": "97bd9f4678b24a0cf869fe9f21cd49ab"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart", "hash": "3e386920be87d5a0e9835148593dffe5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\date_computation.dart", "hash": "37837bd1379e66f38e4a7775b6084d0e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\angle_instanced_arrays.dart", "hash": "3bb154213ca902f8cce0611f87538957"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\prompts\\prompts.dart", "hash": "a0fac1f001876dbce7e5f7094e6a564d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\agents\\tools\\exception.dart", "hash": "c89cfca28fe1bba57bb35e0d01c162b3"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\ai_chat\\daizong_ai_screen.dart", "hash": "2c863a9a421cf4a4dd17059ea1bb8492"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box\\keystore.dart", "hash": "********************************"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\choice_chip.dart", "hash": "1e5182051ef0e48e07643f573e5f4bb0"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\character_type_service.dart", "hash": "8cc0f1307dc5aea49a523382508140c2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl2.dart", "hash": "12494b6f15f091477d72a97539e343c2"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\assets\\version.json", "hash": "457bbc1c02ec83827cf28cac703db301"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\banner.dart", "hash": "94827977f570692122ac770d033aaee5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\intersection_result.dart", "hash": "832666b4f69945b957b6399ec677085b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\math_expressions-2.7.0\\lib\\src\\functions.dart", "hash": "c120332558f0f208eea3d40d263ae58f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\time_interval.dart", "hash": "17db414327e1f763806c112c6f664ca8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\bidi-2.0.13\\LICENSE", "hash": "43cea12e1d141992f166c17e0356d667"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_texture_norm16.dart", "hash": "a39af050125206166a034535f9fbfd7c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webtransport.dart", "hash": "497331f651ef215d8b51429e95e0c9aa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\fine_tuning_job_hyperparameters.dart", "hash": "796e7133469fe5d0f306881993b3aeb3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_transitions_2.dart", "hash": "1674cc51f019878df5a2998c7661bcf0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart", "hash": "939bb15a6ae926447a3f2e4c0f3a5d39"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "build\\web\\assets\\AssetManifest.bin", "hash": "acb8ea1973381d2d36b763a285225cad"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\pattern\\pattern_iterator.dart", "hash": "accb24637ddbe55d7a3f76e4618bdd22"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\long_press.dart", "hash": "7e9cc2885777664a4d8a28ceaaf4343a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\fine_tuning_job_event.dart", "hash": "326e5d220802e5f2998f1f203904d6fe"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\animation\\animation.dart", "hash": "73d837564acafc178f5bf73dc50994e0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart", "hash": "f1c221321c03afe2f57366e9fee54300"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\platform_interface\\file_selector_interface.dart", "hash": "5937c2b1cbdf77126bc2dd93570d3c98"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\llms\\fake.dart", "hash": "457fbb728aa031ec39be35c2f9c6210d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\switch_theme.dart", "hash": "77c0e52dd42c70e9f234e8493da90d99"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\storage\\file_system_stub.dart", "hash": "fb19d486df451c76a62bf3efc79640b8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\resource_timing.dart", "hash": "7a1d80d3a6b17fab735111e172ce99d7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\helpers\\lists.dart", "hash": "1c184e2a9a0ae3bab3e8ae215f5061ef"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart", "hash": "ba5b090b32a94023c53502579cb653a2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\system_sound.dart", "hash": "6c3746e3a4529b0367098600fb9d3d02"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\transformers\\text_splitters\\recursive_character.dart", "hash": "645f25ea944c94173d448a833a012b83"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\from_callable.dart", "hash": "b05a68b737792aa52eaaa4d3e093bb63"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus_platform_interface-3.4.0\\lib\\share_plus_platform_interface.dart", "hash": "7e38424729d139f4ac1ff5183cd59303"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\update_screen.dart", "hash": "5e895327a5c94cb776a75d5cc6008694"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\media_query.dart", "hash": "b071f66d17839dfc4b30db0836e7d5d6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart", "hash": "9de52faf4ac5b8490e2763cc6969b95b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\chains\\qa_with_sources.dart", "hash": "8dd57062b5af41000da25bb181b7bc0c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_transformer\\reject_errors.dart", "hash": "2f711a88a049130159adb3f7867423c0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\list_fine_tuning_job_events_response.dart", "hash": "3f491c8fed3e46cd1cb809336035c7ca"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\collections.dart", "hash": "8764a2799f8899639fbf335bf794c627"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\genre_manager_screen.dart", "hash": "f14661755e7b3d0d7f65ac95538fe3f3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\buffer.dart", "hash": "15563ca80dc06490676ca80b6b98718f"}, {"path": "build\\web\\main.dart.js", "hash": "10b18a115a745f13339608a8ac6de87e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\model.dart", "hash": "51f2a7524889ce0a38b422d5bdfbabb4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\painting.dart", "hash": "e6c5d07cbc53020acc5c08062c8b57af"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\permission_handler_platform_interface.dart", "hash": "d3e01a9f299b192bb14b18062c49e562"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\fetch.dart", "hash": "7bc189c041a9af516afc4cf06fa04a48"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart", "hash": "b6d10049d482e2a2e85b4c49d12997ec"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix3.dart", "hash": "64b9fc5ffdc9f1ba801b6ccf099347b1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\modify_thread_request.dart", "hash": "fa8d56a9677692eabd2fa80daa30a267"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\saa_non_cookie_storage.dart", "hash": "9ba73a099cc9ea4f64804786f0b64d0d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\live_text.dart", "hash": "d970423c6488cba52d3585e0221e57ba"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\lib\\device_info_plus_platform_interface.dart", "hash": "4b532e7a43e7a2a180a49c5869ec8ab4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\never.dart", "hash": "238c701652f1f5ad9ba928994a96e608"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_flutter-1.1.0\\lib\\src\\watch_box_builder.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\utils\\utils.dart", "hash": "2ab5355eeabbc36b34136fb85d285221"}, {"path": "D:\\element\\flutter\\bin\\cache\\dart-sdk\\lib\\libraries.json", "hash": "699f9f78cf04a3745fc01633f735be9d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\date_format_field.dart", "hash": "a9e35bc352293fff8cee637c13d9bcc8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\annotations\\hive_type.dart", "hash": "b26d0a2e3e209b52ffb697f829ec46cc"}, {"path": "D:\\element\\flutter\\packages\\flutter_web_plugins\\lib\\src\\navigation\\utils.dart", "hash": "7479f9e2e67c83e5b96a025202d94af6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\netinfo.dart", "hash": "fcc009cb2fb000be4e3c251e9777f7e0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\release_transformer.dart", "hash": "45a20da2b86984fa0b29030dd190c75d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\root\\get_cupertino_app.dart", "hash": "5c115dfed1c0d53c90890512ab626403"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_object.dart", "hash": "f79175f033d797e770dce9016b32884c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart", "hash": "46a5ea4411d0fef172edb219362737a3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart", "hash": "29eb69935c586ca665d0f6f7aa84a06d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\bidi.dart", "hash": "68634d4df864077f507d84d92953a99b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart", "hash": "5af5a2fb59f9c015abbda254a35ea7a6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_completion_request.dart", "hash": "7753025a4332ffe91795cb54b0354dca"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\distinct_unique.dart", "hash": "32a430474c588e6a5dfb093a222e9f48"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\colors.dart", "hash": "9cd03844c4e859875c10c9708556a0db"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\lib\\src\\mime_shared.dart", "hash": "c2f30f0829e63ccf0449de5982e324b4"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\auth\\login_screen.dart", "hash": "581f94d2cdd49e6e3b41a3721ebc6592"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\chat_session.dart", "hash": "b0f89e2d8f1aa4701096d2c22e34398f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\quaternion.dart", "hash": "82a52b42ca10c86b0f48afea0cbe9ac7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\storage.dart", "hash": "1c2e53982b49fb3a168b99dad52cf486"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\geometry.dart", "hash": "4e4d2071604e3c34d057490624ed1c98"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_logprobs.dart", "hash": "e037e57619da695a72a884975b2f2876"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\lib\\src\\mime_multipart_transformer.dart", "hash": "531d1d96bce7aa59a6109c02ac538cb0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_web-0.9.4+2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\lazy_stream.dart", "hash": "1649ee82914f6ad1fd46de466dc03378"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\lib\\mime.dart", "hash": "90fb56de843f1f933c2ba8ec945fa06f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_flutter-1.1.0\\lib\\src\\stub\\path.dart", "hash": "365bdc6bf007b063b23d731171b74f7f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iregexp-0.1.2\\lib\\src\\iregexp_grammar_definition.dart", "hash": "f3be96f1b9c4aff90f110a27a00bfd27"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\annotations\\hive_field.dart", "hash": "c01f3dc3ecfb5ddf08d6b002c90aabfd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\frontend_server_client-4.0.0\\LICENSE", "hash": "43465f3d93317f24a42a4f1dd5dc012e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\future.dart", "hash": "8d3f31cb53177f3f6315575373249597"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box\\change_notifier.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\fine_tuning_job.dart", "hash": "d5f650a1fa0ed8f0412eddf7b04a6964"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_step_details_tool_calls_code_output_image.dart", "hash": "a5eabba5c07c84f33ff08d6103e72aa3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart", "hash": "ef78266d8f88424f55e3135455ee90cf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\just_audio_windows-0.2.2\\LICENSE", "hash": "725fcdc4390d28122a81eac334567474"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart", "hash": "ce7c719e306ad9ab02b19aafbd70178c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\just_audio-0.9.46\\LICENSE", "hash": "e9898cbd2824d4e3dfa968f76821ca50"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart", "hash": "7b4c848f6306cf60639bde0049afdd81"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\archive-4.0.7\\LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\chains\\chains.dart", "hash": "20b491eff3b64c4f4bc44fbd2d8f8f55"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\code_block_syntax.dart", "hash": "8d4a2c9ffa420a1069785aedb5edca3d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\src\\types.dart", "hash": "83bb9dfd0d336db35e2f8d73c2bdda85"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\app_lifecycle_service.dart", "hash": "53770109a0120e0a9ef63ce7f00c4259"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart", "hash": "5ab06ead51a9dff19d214cb198040eb3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart", "hash": "7d4555efb4e113afca69dd69e91024a8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\separated_by.dart", "hash": "04b800051d2d913cafc3733ee1bb21c1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\search.dart", "hash": "054abd6fdcd55ea92434e7f591f3785b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\iterator\\iterator_wrapper.dart", "hash": "fd0c23f623d3a995a1de6d8a34bb6498"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\debug.dart", "hash": "f2138801d3af6774b0cdb3ff8f966b8c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_color_buffer_float.dart", "hash": "1be3ac6ed867822ebae3ec0fe23bf389"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\base.dart", "hash": "2b7ef9ec45517fd0fa80f7704c752439"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_named_tool_choice.dart", "hash": "b1cd39f046db1e078be8f709a72dd62d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\media_capabilities.dart", "hash": "d2e6e8548dd35829a6198324074055a3"}, {"path": "build\\web\\flutter.js", "hash": "bcd27ed3ea43abb33a14be759e9dc1d9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\cupertino.dart", "hash": "d0d99e4aed47c375c97c9822cfdaaa28"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\flow.dart", "hash": "e92d23e94e235dd63649d0e26f8314b1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_markdown-0.7.7+1\\lib\\src\\style_sheet.dart", "hash": "9b0892b5181f026206816fbf9ca61363"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\llms\\openai.dart", "hash": "fa94a01bcdf38a0680da09290acc0988"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webcodecs_hevc_codec_registration.dart", "hash": "1d08fc8c6a5afb14679a1fee86e6e3fb"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\novel_outline.dart", "hash": "b24cf7f82c7a9f12016836679da1f6f7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\transitions.dart", "hash": "d36e7d651314b6c9bb80b1616ae188b2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\trie.dart", "hash": "f67497a47a5f8508d53dea861aa1e7ef"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\flavor.dart", "hash": "fc53938b545fafdfb7a5ef073a1254b5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\analyzer-4.7.0\\LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\stream_subscription.dart", "hash": "e2d2090c2a39f7902893e64150fe82b9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart", "hash": "413dfcfb58932aef16c971e1148c1d3f"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\novel_continue\\novel_selection_screen.dart", "hash": "98abc39e9398bafc46d695df6b99812e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\html_block_syntax.dart", "hash": "875b8db0bc2c795975d4df8ca03815bd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\empty.dart", "hash": "ac4e4c808dab498eb2d5c7f813a5006b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_storage-2.1.1\\lib\\src\\value.dart", "hash": "e37fcd8f5a1ec3bb149324368e982b89"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart", "hash": "6a637ada9a55a237512011f664489a15"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\utils.dart", "hash": "81516bdeab3e99ef43b2e9849d1def01"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\runnable\\input_map.dart", "hash": "e3bbd39d42857f6e740f497b8ea19ece"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\iterable_extensions.dart", "hash": "76ab907faf78cbe92901e32312d4a840"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart", "hash": "b7856aab58a26c2a46a4735f1d2ed4b7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\text_parser.dart", "hash": "3fd53a129b1104ff66ae27f3ccfe53b7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\constants.dart", "hash": "1ec635f2db97328558affe7a0c49fdeb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\reference.dart", "hash": "e7a9dcfeea903e16ba7ddc8cc31960d8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile.dart", "hash": "36b808e976f035411a6b13a09cca7717"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\compression.dart", "hash": "431a4f8163a783c176877903a4c18025"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart", "hash": "ba0c8dd8a234a449050e76d22738480f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\fun\\standard\\length.dart", "hash": "a88931b270e025b119efbbc326f2207b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\predicate\\character.dart", "hash": "c1d88c6f9a0dbed4be35c285ffac4da6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\ranks\\p50k_base.tiktoken.dart", "hash": "f4261ebd8f80b684da2dfb176611a0bd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart", "hash": "ec3a5f7a8288542858278a8702578709"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_typedefs\\rx_typedefs.dart", "hash": "4a896d0615494befae74c35d04fec250"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\extensions.dart", "hash": "26fd110a0fe22888f88e4d48e8163c21"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webmidi.dart", "hash": "3ac71c621e176bd5ffd2c794292dd2e9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart", "hash": "769e3974284ea91f60d3322d7322a979"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\math_expressions-2.7.0\\lib\\src\\algebra.dart", "hash": "19d9e26f689cd5e11d57be61d00b3945"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\banner_theme.dart", "hash": "3130351c8333143b70539b0a0bef2c9d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart", "hash": "fd9b487c6b333f8de2f5b2fbe769536f"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\export_service_web.dart", "hash": "f43dc5347def44a7fe9fd3361d311b6d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart", "hash": "********************************"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\theme_data.dart", "hash": "f318d9440c54a7858dc6e64e01222e1e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_contain.dart", "hash": "d97ab713e0f59c5223dfaa0bc527db01"}, {"path": "D:\\element\\flutter\\bin\\cache\\flutter_web_sdk\\kernel\\dart2js_platform.dill", "hash": "4bc9511ec74c125b4eabcc4a66819845"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart", "hash": "5c32703ac32c4835d961b3c55f8f8498"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icons.dart", "hash": "0fb5a81c723bbd506468f1ed5a811a48"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math.dart", "hash": "703f2b29a9faedbb501bbc2cd99ba7b5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics_compiler-1.1.16\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\scribe.dart", "hash": "fc0d5385b1ef6f854be4e193437b39b6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\interface\\output.dart", "hash": "7165182d7d6f17f6c325a5b69b7274a2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\binding.dart", "hash": "73e482e3ef72695bcdaaf4888ca76868"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_android-12.1.0\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\src\\model\\web_browser_info.dart", "hash": "9e887cddbdf6c6c7c650a995832b127f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart", "hash": "fbf98f47029acb307401fb5299babd1b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\mediasession.dart", "hash": "8a27b04fdcf4b9f1024072549363b25e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\object\\hive_collection.dart", "hash": "f083ee7c0f8875e81b5fd6e33fde3ed5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics_codec-1.1.13\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_8.dart", "hash": "9dbdc6f759d68f1ba1b47b561de1e299"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\property.dart", "hash": "9f56fbe65bf797a2eba907e0181e69ca"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart", "hash": "efcda216cd91049b2d762cbac411b0ae"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart", "hash": "f45299fdabda0bd3b2ed77068c7d1de6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart", "hash": "1d3320ee2aa0dd0cfcca1c98a2f3fa16"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\resize_observer.dart", "hash": "a1f69f2ce4c211abb4f4ed797b152b01"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\flex.dart", "hash": "52cafa00c830c441c43e99eb3e888c37"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\helpers\\renames.dart", "hash": "a148766f1d7ee563c9581773c40b7641"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\LICENSE", "hash": "5b9e0abd5b94088a3b85c38473db456a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_literal.dart", "hash": "8388d5e13155ebde873438c26dc4ca33"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_svg-2.1.0\\LICENSE", "hash": "a02789da8b51e7b039db4810ec3a7d03"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\background_service.dart", "hash": "819c12134d3c2d064a4fb0353d2d347f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart", "hash": "52be999baf3515d44f113c57a7f7d38e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-3.0.0\\LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart", "hash": "5b4049c371edb699a77a1bf698b47e07"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_moderation_request.dart", "hash": "6a9eb0a3a313eea4b9ff137c0602a82d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\ink_well.dart", "hash": "46a436593c7a3f25b782ee5e6ccecf46"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\registry\\type_adapter.dart", "hash": "ed743446165700520a88ccc56514877d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\utils.dart", "hash": "ebf21341320c02b09bfd8dcbfc683398"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\matcher.dart", "hash": "faa18ee55924a5c65995875c94338d98"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\message_content.dart", "hash": "5c00d770d8f8985e9f996d8793c5f4cd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\app.dart", "hash": "24b97b96bea7d297a53c0ca6171f2b17"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\retry.dart", "hash": "f144b42678425595af39c360b9bf8d8b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart", "hash": "527d1c362e89cef34dd8f540559b44e0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\vector_stores\\models\\models.dart", "hash": "4e082b9e5f4976c8d299409f9aaeec30"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\css_class_set.dart", "hash": "fd47de61e362c730e345626317a8fc44"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart", "hash": "b11666131177a6ebe97ffd60e3dac32a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart", "hash": "1f418dd0c28e28b509d8179a39898a5a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\pattern.dart", "hash": "cd6d0f606129e54552c5fee950e32bfd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\orientation_sensor.dart", "hash": "7c2fdebd830f06bff067e79104a025b7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\unicode.dart", "hash": "c692323b8d9e3ed3c4c134ba07ab94e6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_markdown-0.7.7+1\\lib\\src\\widget.dart", "hash": "6aa9aaeedde9248c2a398619524604bb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\loaders\\text.dart", "hash": "ba1d979ae7c5b0866c43e963f1411b60"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart", "hash": "93e2e064cd4ab7de8bf34176ac215f83"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\model_io.dart", "hash": "5fb7e9c3f5cf31adc423cacc249aa386"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart", "hash": "cf39132e96645ad4603d01e2a71f2ba6"}, {"path": "build\\web\\assets\\fonts\\MaterialIcons-Regular.otf", "hash": "7351d4194c70192adeeac983cf3b81d4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart", "hash": "4213cdf8a94244f8d19a59844563dd53"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\message_content_text_annotations.dart", "hash": "2863097a5ba2d2ff91c98a1b713c53fd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\math_expressions-2.7.0\\lib\\src\\evaluator.dart", "hash": "4312e63402df676f9ea09227988c28c9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\switch_latest.dart", "hash": "a52ae2e097914c25b04abb01abf02183"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\build\\web\\flutter_bootstrap.js", "hash": "476788d4a41330661d53e1fc8a61cb97"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\action_chip.dart", "hash": "51733e4ec61656353b10519c6af9ce23"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\moderation_categories.dart", "hash": "e75bb67dda99f25c28b41c3595c1ad3d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\helpers.dart", "hash": "9e1daba981bfab0a1424950a97970ca1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sheet.dart", "hash": "33a79c004db8a1a20f9b7607d1575e56"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart", "hash": "40f23c5e8322838cda4c344520aa0beb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\speech_api.dart", "hash": "a6378f15238416e3ee0f731025017a99"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\array_index.dart", "hash": "957b26abb07335e5c9625f59b2dbf200"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_subscription_transformer.dart", "hash": "9422bcb42f545a3d7fad54a0559effc2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\widgets\\common\\animated_list_tile.dart", "hash": "d66f0a25caa8cb4285cd9cb6fc1c0d00"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\scheduler\\debug.dart", "hash": "da2fb5c7eaae1fd58b34e80c4dad2cca"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\retrievers\\models\\models.dart", "hash": "93689b4591a429034d04bc071c782355"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart", "hash": "f11954058bc60e8c9403c769a89da56f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\utils\\chunk.dart", "hash": "dd4290b293336035ccbee6e7f4ec809f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pdf-3.11.3\\LICENSE", "hash": "5335066555b14d832335aa4660d6c376"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_file_stub.dart", "hash": "a97e65bfeebec666a235b7c6a4ac0d66"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\glob-2.1.3\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart", "hash": "cb6197499c79351555076b3a8ecbe352"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\flutter_logo.dart", "hash": "494881558ae50a1d5273ceb66b43ed85"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\lib\\src\\messages.g.dart", "hash": "814815839a4b6d2924a5a8661780b0cc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\preprocessor_options.dart", "hash": "9f788a6e170d7968e9906e4d470e07f7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart", "hash": "2babfd7c893533e7f7b3d1edcc075b64"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\predicate\\any.dart", "hash": "35536afe2f48001aa7c588b131051b83"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\animation\\animations.dart", "hash": "1c738923184d679b731ced5d2bf84a9d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\ordered_list_syntax.dart", "hash": "643da60487339ee552c39cd11f92dcdb"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\text_form_field.dart", "hash": "3ce8ba84ecccfdb74916c37d7022a08b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_android-0.5.1+14\\LICENSE", "hash": "219aaa419a8f494f271df2dc1b016607"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\transformers\\text_splitters\\text_splitter.dart", "hash": "ee47b252bf9ac4487a91b413666f13e1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request\\request_duplex.dart", "hash": "fa9ac060d1fd59665e0e4f0a27812bf2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\colors.dart", "hash": "243ee4a9d79acc2c478caf01088decfb"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\adapters\\writing_style_package_adapter.dart", "hash": "b5ff604dddcd1ace16cb28ec26496f4c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\common\\special_tokens_set.dart", "hash": "b3c7514108064d3178b010303d43fad3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\replay_subject.dart", "hash": "7929b4d3e79087536edc9cd260f8d4c7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "build\\web\\flutter_bootstrap.js", "hash": "476788d4a41330661d53e1fc8a61cb97"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\question_answering\\question_answering.dart", "hash": "20cccef5a71e1fc59b74ba9b0bcad16b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\index.html.new", "hash": "7c20167368b8f1b6ddbfc83dac676c27"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\output_parsers\\output_parsers.dart", "hash": "ad67c7bedd0706d6ffcdd2ed807aad93"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\arc.dart", "hash": "06e19293b41e3c13506f5eacecfa4afc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_thread_and_run_request.dart", "hash": "4b63a17d80d921804f66afe18aa2eedb"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart", "hash": "a5df6e3153b654738dfd5a67c799e6d5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webcodecs_av1_codec_registration.dart", "hash": "c1eba6d2efaaa33fde653496c90cf15a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\payment_request.dart", "hash": "9f20dec3fd81898daaa4ab5f9547874d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\backend\\js\\native\\backend_manager.dart", "hash": "ca6bcefe281903472e9d8c387baf3260"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector4.dart", "hash": "7d33539b36e15268e2f05b15a9f5e887"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart", "hash": "40110052956a9ba346edc09b14dc1c14"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\expression\\group.dart", "hash": "f31a685ec42e95decf8c1937de3a5856"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\switch_map.dart", "hash": "56bb06e8c5f7f7892ae9eb352dd91f9e"}, {"path": "build\\web\\assets\\assets/images/coffee_qrcode.svg", "hash": "ff699580c3a753bd0b6b043d50888258"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with.dart", "hash": "46c6500112cb203a46608825824d4d52"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart", "hash": "dc64ebb08fcf29bdc05884be98af7daf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\emojis.dart", "hash": "21d9507f58e796f72d67142ffba078bb"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\team.dart", "hash": "193d1801b76c52075287857a01d3fe83"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\routes\\default_route.dart", "hash": "adb02e6c49e68560f0ee9877b7c37ba9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\response.dart", "hash": "db16eefb533cb0d50800fa5d457d6c5a"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\character_type\\character_type_screen.dart", "hash": "0a63801b3fe28c995ead86aa0e85011a"}, {"path": "build\\web\\.htaccess", "hash": "c41eb092550261ea39b88529e3a7aee3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\localizations.dart", "hash": "3988183ed965c8f6aa3c7f429a2f0ef3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\definition.dart", "hash": "8680f57e6ae9665a5f051c06c1efc688"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\end_with_many.dart", "hash": "902509bf876a10a7b6e534a1d24fb476"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart", "hash": "77c012e51ce5fb5b6758ed47ee29af56"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\navigation_timing.dart", "hash": "a842a5f8a2b5ab393b7d7e063c962b16"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\divider_theme.dart", "hash": "34fbd21bc1ac0d1f0412b22e47041ece"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\http\\html\\file_decoder_html.dart", "hash": "ef6edf4f985e8a1ce5522b869b40fe33"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\timeline.dart", "hash": "ff300e7139d4b384c9ba4c12a100f68b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\horizontal_rule_syntax.dart", "hash": "c4d36aa28d4b58a5855596f2fe906901"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\chains.dart", "hash": "99f1e26ba9f959ed755df1b0f9121218"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\opengl.dart", "hash": "21baec3598b81f16065716b8ee97c8bb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_stream\\mini_stream.dart", "hash": "1bab761ae0c6f07432a053027a30099b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\expression\\builder.dart", "hash": "d2b684e31e63b6c876b2c0266705447a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\message_file_object.dart", "hash": "c53e3ac1b5f2cb0591c01c9b17e9e88b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button.dart", "hash": "c85987e2ad461c8b574f838c3e7a366d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart", "hash": "220eb17aa14783375f802e8d5cf5c49b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\export_platform.dart", "hash": "b885f1e750151f336b62585cce9305d6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\query_selector.dart", "hash": "072bc29df9af18240c9691c60edcc988"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\chunked_stream_reader.dart", "hash": "14acd577a81cd5aa871c66f430b95d97"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\completion_logprobs.dart", "hash": "023757c7fafa530e1963e5d0eb3ad821"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\predicate\\pattern.dart", "hash": "d881c458d06573eb887bdf0f3ce9f586"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\animation\\curves.dart", "hash": "6ac08531373f9a0cf8243363385bdab2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\spacer.dart", "hash": "977b101929ac6f80c9dab61b4f232bda"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webcodecs_vp9_codec_registration.dart", "hash": "fbc14c398e33c1635b85a027d1b1bf51"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart", "hash": "cb97906222d39edbd20f2db91ee3776e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\permissions.dart", "hash": "210c048047ef1101085956c33ae275df"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\array_index_selector.dart", "hash": "17f88d0cfa82fe51065bf10683da5b37"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap.dart", "hash": "347512ceb21460333618b527f4bbd7ce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\lib\\src\\link.dart", "hash": "f40d1d82dd5063d51b2e915133377e7b"}, {"path": "build\\web\\build\\web\\assets\\FontManifest.json", "hash": "99914b932bd37a50b983c5e7c90ae93b"}, {"path": "build\\web\\assets\\FontManifest.json", "hash": "f55763f32b62e50aa0cdf351aa7d73f5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner-2.4.13\\LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\analyzer.dart", "hash": "17aa54781ed25267f20b106de6b6d59a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\models\\models.dart", "hash": "c47b905cc8c7192eae6f0aff21d9b428"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\title.dart", "hash": "ebbb5c061b2adb32764c3c2d1ec6ef3b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\controllers\\theme_controller.dart", "hash": "b42efb711f34350956fc5aa2042052a5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\binary\\binary_writer_impl.dart", "hash": "7f3d8ecd3382ba1196fa6ede8b4c8fe8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart", "hash": "14d158923653b3f46803542f256b11c7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_floating_header.dart", "hash": "29666cfc794fd78ec2462f291975ca43"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart", "hash": "5b1c50bdd6d4c64f4c6bf4cba82d48e4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\LICENSE", "hash": "e5c509c88ba5deea93f23433c98752c2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart", "hash": "e4bcca669e3c0d0239cc2ebcecac0438"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\sockets\\src\\socket_notifier.dart", "hash": "ef2b1831d26ac005d53f3545674ad2a4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webxr.dart", "hash": "389e1f91987c62edc204aeedee11875e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pubspec_parse-1.5.0\\LICENSE", "hash": "abb5a1fdfd2511538e3e70557aad0ba1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\assistant_object.dart", "hash": "f855063aec5671a169a89f052cbbe438"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\websockets.dart", "hash": "584d768370a6ea5d7aa43bc6dc941786"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\bottomsheet\\bottomsheet.dart", "hash": "3b3cebfaacb20c04e1d1c4160541eee7"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\novel.g.dart", "hash": "c3513b081b0e03d001890e0018308f21"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart", "hash": "1dd5b37860108a769459a04adbc418ed"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart", "hash": "28d7ea566a4cd9f1e4455efd86f424b0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart", "hash": "a01365700ffe2160f5566cfa8768b4fb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\number_parser.dart", "hash": "6e3f753d2e2717b4f36ffa7c34b58dce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\intl.dart", "hash": "9947d163dc33c2a6804841d23ff4b9e2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_blend_minmax.dart", "hash": "91dce3137bda013efb41522091668ef9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\flutter.js", "hash": "bcd27ed3ea43abb33a14be759e9dc1d9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\lib\\src\\device_info_plus_web.dart", "hash": "0ec3fe323126396baa61f377f500b8f6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\url_launcher_uri.dart", "hash": "3cb04add978cf19afa2d0c281e4c80b2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request\\request_redirect.dart", "hash": "5350b092158bf630e79ddc7c89475e1d"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\embedding_model_config.dart", "hash": "769dbdb79b9698a7f8ecf938a064df1c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\process_text.dart", "hash": "53c1ff11b2873a67a9e4c0277a31a856"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart", "hash": "9ee10f71d143dd2afab234cf329b6404"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\button_style_button.dart", "hash": "039f1af2b4e1859cb3852994632bac52"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\storage\\storage_screen.dart", "hash": "c3cf7d3544cba8ccadc167b638c9301a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart", "hash": "65fda21c9512703200f5b93d08023086"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box_collection\\box_collection_indexed_db.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_style.dart", "hash": "bbf75dc0e0515e7a133e7eea9052668b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_animations.dart", "hash": "82e2cce258d43f85fa85f1f226e8a30e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\fenced_code_block_syntax.dart", "hash": "90dc2221baa9718ccc42c473dae1c601"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button.dart", "hash": "ad1cdc9c1d06d8af94266565ad966f3e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_assistant_request.dart", "hash": "0c6ebdc851b382d5e4a78bc7a0702c54"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\function_parameters.dart", "hash": "9759eee200e36f7d0fe7c7d6c8c2ba77"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_flutter-1.1.0\\lib\\src\\stub\\path_provider.dart", "hash": "ec4f9a6be8569574549b1ae6b9113919"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\character_card_service.dart", "hash": "a1af2e9373d9795713e35e652e92e7f3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\tree.dart", "hash": "01d34f3007e4fddbaf4794395c4d9276"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\LICENSE", "hash": "c23f3b290b75c80a3b2be36e880f5f2d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\csv_to_list_converter.dart", "hash": "e84b76482676b5676e81357cf5268bc3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\mediacapture_streams.dart", "hash": "888f5d95b09ab34de2c9d37bd7a33077"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_markdown-0.7.7+1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart", "hash": "c7acb3b122d08afb8b40130e88a0dce7"}, {"path": "build\\web\\icons\\Icon-512.png", "hash": "66573d37ae61532f6666a2496c10fcb9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_stream\\rx_stream.dart", "hash": "f6a385faf410138c16a10876bcd887e6"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\assets\\fonts\\NotoSerifSC-Regular.otf", "hash": "dad74d67a575308967abcd6b39544602"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart", "hash": "9dfe03ebe247c2eb3420b00c80b61a44"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\system_channels.dart", "hash": "9afda1dc3f04a9751c25be745166dd58"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\output_parsers\\output_parser.dart", "hash": "95fde119e4a87d0386081314d778021a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\embeddings\\fake.dart", "hash": "300828390af04c02175eba9a6f347b19"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart", "hash": "b8fb4215060bb74d8025202cabeab63a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\eager.dart", "hash": "2a5ea48301e3de49528660d81bbad42c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\lowercase.dart", "hash": "044ac7a861e88a6b5e7e2d2c59ccb7bd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\settable.dart", "hash": "f5f653af8a150de004f1b3ca1633bceb"}, {"path": "build\\web\\assets\\assets\\version.json", "hash": "457bbc1c02ec83827cf28cac703db301"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\drawer.dart", "hash": "d460667f13c0c9c2ddb60902d2d92663"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\aabb2.dart", "hash": "8a05c4ee4d75a485389f2e5c2f6618e6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\range.dart", "hash": "57de88edbfb0d8951419051441a6de56"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\child_selector.dart", "hash": "981d05463f56b80bb0d7af4cfc606332"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\uievents.dart", "hash": "8b3fe6eb34b48a71f0c3e444fa83e5fa"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\nginx.conf.example", "hash": "3eabafb024a685f2d3b6f8139b54a757"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\autolink_extension_syntax.dart", "hash": "630b89c4b107730184bd96ae62c1f579"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart", "hash": "076435cbd25cf350cf50205dd257e849"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\prompts\\prompt_selector.dart", "hash": "0d482adeea69debb7f8d41af74bcc63b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver.dart", "hash": "6faa3584c97f1583d85c67bf6fd53873"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\emphasis_syntax.dart", "hash": "747b30068f882ac4fe40d0d50d76c8c6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\object\\hive_object.dart", "hash": "08b848f81523e9f11afbad3153f6dac8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\list_syntax.dart", "hash": "a9aaf40eb961cf4e3c16f1002236a8ac"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\physics.dart", "hash": "ffd7e9991334466f08df7afe0c721048"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\url_launcher_string.dart", "hash": "ec94194f35d48443f468a3b06ef69845"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\sockets\\src\\sockets_html.dart", "hash": "894b3bf91ee0a31a23ff31e7f055cd2d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_storage-2.1.1\\lib\\get_storage.dart", "hash": "304558adc039bc8931a4676123a605f9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\llms\\base.dart", "hash": "6642c8a24b3771fe2d9f98a34ccfabcd"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\.dart_tool\\flutter_build\\b7a4acd900463a69943b557d4d9a654f\\main.dart", "hash": "5a72013a2c84e0ca54df07d9a66f1ff1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\encrypted_media.dart", "hash": "c53973182da208da61ea4f0ffd71df8e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\fetch_api.dart", "hash": "ec46d8034e06cf822932436f342896a8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\delay_when.dart", "hash": "064ceadd31d0b29fc59188b2c4d45db1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\plural_rules.dart", "hash": "2241f880365723564463d0bec35a4ba2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\langchain\\models\\novel_memory.dart", "hash": "cc060ade63d8aad0ee36e4afb5720b4a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\permute.dart", "hash": "8171c3b0d66f560aad82b73d43393092"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\mst_content_hint.dart", "hash": "2df5e106795b5fd5f73cc1505132b57f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\get_utils\\get_utils.dart", "hash": "95007024281ddb0599c66aeb0ace950d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\constants.dart", "hash": "9a463f361999508124d9da4853b1ba5c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\json_annotation.dart", "hash": "532a272d043c3dccd91b63d1b428dac9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\flatten.dart", "hash": "b9f39f1eac6d7a0e9964cb4c7b2cd04a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart", "hash": "b32917544d6176139cfa3618ca522b14"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\future.dart", "hash": "443fe4357544b85c13ef051cf37a602f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_message_tool_calls.dart", "hash": "e86ceb806aa1cd794ea37c742bcf69d9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus_platform_interface-2.0.1\\lib\\package_info_platform_interface.dart", "hash": "ffacd89f6848591ec15ef8513943ca8f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart", "hash": "9849718d7826b98fa09e037accf9ae21"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\theme\\app_theme.dart", "hash": "59d39daba48ff4573f9e53f56ca91bee"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\langchain\\chains\\detailed_outline_chain.dart", "hash": "b220ad7b39d54914298d43924a9f714e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\moderation_categories_scores.dart", "hash": "4b7496f8cd6b6908be867743304d1d4d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\icons\\Icon-maskable-512.png", "hash": "301a7604d45b3e739efc881eb04896ea"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\math_expressions-2.7.0\\lib\\src\\parser.dart", "hash": "1062eff8d707086e9147b694103b53e9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\lookup.dart", "hash": "b76abc07781824bc4c06d11631188db0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart", "hash": "e7ca145d8d308187bc0127bd941b1759"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gbk_codec-0.4.0\\lib\\src\\gbk_maps.dart", "hash": "2129e979b0da24210817c842aa99c98d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\models\\models.dart", "hash": "58ba96342f5caf3f5dc1b177d82daaac"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\link_reference_definition_syntax.dart", "hash": "0beb13552d9077a84871c3a1fb1e703d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_font_loading.dart", "hash": "9f7ce6effb58ed1966c1b1be3afcc6d5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button.dart", "hash": "917ff734e7ac8749015973f521ffcae4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box\\lazy_box.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\storage\\in_memory.dart", "hash": "f733ce1d0910c06df8d7add13eedf87d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\line.dart", "hash": "2323b77c264142a16f4e80829f6b3de6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\routes\\transitions_type.dart", "hash": "622217056b484b03637454de2589c899"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\box_border.dart", "hash": "********************************"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart", "hash": "39d8ca399102782c09b864fa92d51f3c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\link.dart", "hash": "c36f00a660d9aa87ebeab8672ccc6b32"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart", "hash": "c337b850a7c3d9b2239394993aeeab6d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart", "hash": "63701253bb9a85c00b68c2d51358bad6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart", "hash": "f6879dbb0a0b22e90c61f21ebf5c440e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart", "hash": "2b8123df092f0565cbb936af3168dc37"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\oes_element_index_uint.dart", "hash": "f6aa572e7febf8e0269780f1ef8928c8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\response\\response_type.dart", "hash": "471b1d3c1fd0179fa7105ab0aa82df21"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\greedy.dart", "hash": "c138ee7ea69a6621403b3ba6973d4d7b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\ranks\\index.dart", "hash": "d095a6029f6133a16e5d5f62d89e8329"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart", "hash": "d8b218966d397dae64303cdd7d11b33b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_5.dart", "hash": "a2f8f444e0f2d008fef980dd8df0bcac"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\sanitizer_api.dart", "hash": "8d529a9c9b9eb4ebaf4051f92166372b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag.dart", "hash": "c6f3d5ab867a5b665a50e0af11d048d7"}, {"path": "build\\web\\icons\\Icon-maskable-192.png", "hash": "c457ef57daa1d16f64b27b786ec2ea3c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\texture.dart", "hash": "238464b246c0e0f60bc0fa9240909833"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart", "hash": "2285a845b6ab95def71dcf8f121b806b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_run_request.dart", "hash": "3d9357730ef62c6e20f11be4751f7584"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box\\default_compaction_strategy.dart", "hash": "32ef2d2128b50f494da6ea7571d1f7f4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\schema.dart", "hash": "03fc300bb2cd67c157dc2d6992e507c5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_depth_texture.dart", "hash": "af699860aa1d81640ccd60196bddadab"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "build\\web\\favicon.png", "hash": "5dcef449791fa27946b3d35ad8803796"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector3.dart", "hash": "a1e740a70209acedc9ba1bff7141c14c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\lib\\web_delegate.dart", "hash": "c2f3dc321195ebd6a9972cca519f3aba"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\types\\x_type_group.dart", "hash": "826066d6663c91c94cee09406ded70be"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\ignore_elements.dart", "hash": "908b86c4378330e5b303026c8c3e29aa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\fedcm.dart", "hash": "eb860bd33912658cc3569f94ce6cd7f6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\selection_area.dart", "hash": "fd1e888f48f9b2411ee9f3d8a5146397"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\web_locks.dart", "hash": "d9468725a679cc7859966763773626d0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\function_object.dart", "hash": "1a258a83cf999dbd9a6a2e3cd99a8fb8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audio_session-0.1.25\\lib\\audio_session_web.dart", "hash": "b32a09d2c3920af68f4c14d3e4befb4a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_counter_styles.dart", "hash": "8bc41708c1ce9560925bd8a19a92d8e9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart", "hash": "2abc41676d17f2f12e4878a82007cb3e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\html_renderer.dart", "hash": "00c535bbef0e9fdb6d95feaf9d9a5132"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\frustum.dart", "hash": "218ecb2798a6fb1ec08cd5c993d98269"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\heroes.dart", "hash": "56c2a3b80e89450b342a593821e6cc33"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_instance\\src\\extension_instance.dart", "hash": "82ee4f24a64f95705a8fce3c7a7abd55"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart", "hash": "f10d1c73e161d0807716dee4ef148c99"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\oval_border.dart", "hash": "a4bd9e0add3bd13842fedfbd33ba0192"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\expression\\expression.dart", "hash": "8df371260c5b1ceaf0ed7af2fc66ed20"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\simple\\get_state.dart", "hash": "5c96abd839a67be1964ec95fb7114417"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart", "hash": "1cc862e37a6af483d4f60fdadd2f291d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\util\\delegating_list_view_mixin.dart", "hash": "c17576f1b73a93c4effae038a1e2a23f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_key.dart", "hash": "6a18f9347e6e639ebbbfb0a0ce98bb71"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\core_bpe_constructor.dart", "hash": "835e1c1fe4cdf72a3582566cb66183b3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\line_break_syntax.dart", "hash": "12410bbe96093cf4502a61dce5f3a47a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\readable_stream\\readable_stream.dart", "hash": "35f953bb34bdec4f089ba72fd2896e05"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart", "hash": "a2701656bb3160ea810ab576c50cbd65"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\token_buffer.dart", "hash": "c616f21afb8df562825c373d04165c23"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_instance\\get_instance.dart", "hash": "5e1aaff04d89eab1be7e5ec99ad760bb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\permission_handler_platform_interface.dart", "hash": "54e3fc58f0992b887be63771a3d82202"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\lib\\shared_preferences_web.dart", "hash": "5261c2f8204719c9c489eed805f72cdd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart", "hash": "c6361b93d4a266527cc473cc2570f714"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\msix-3.16.9\\LICENSE", "hash": "639652e8ee16e1fa5f948be5d20e25a6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart", "hash": "0bc32d9519ad188655722c1b5df8e896"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart", "hash": "e4523a14b383a509595ff9501c8cedf3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pub_semver-2.2.0\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\text_direction.dart", "hash": "45f61fb164130d22fda19cf94978853d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\wasm_js_api.dart", "hash": "9a3ffc11698b5af44402167cded39432"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart", "hash": "8b1f55bdc7ddfffc5a4d677052b1b789"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\route_manager.dart", "hash": "8975983116a105d06cdcfa9ace361373"}, {"path": "build\\web\\assets\\assets\\images\\coffee_qrcode.svg", "hash": "ff699580c3a753bd0b6b043d50888258"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\animation.dart", "hash": "e76c07fd6945a4eadb2aeebf87b643bd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart", "hash": "8f2cd2999e409c0aae2b8dab83c955fe"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\controllers\\style_controller.dart", "hash": "d0f5e80a1bb0ae67515572e4d911b357"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu.dart", "hash": "1c5c3812ee79de590644bbc24213b346"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_markdown-0.7.7+1\\lib\\flutter_markdown.dart", "hash": "e147cdea18f83c50d664e12cc6e8fb38"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\download_canvaskit.py", "hash": "6cceae68480ed769cbce9cf0b4b9f55c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gbk_codec-0.4.0\\lib\\src\\converter_gbk_byte.dart", "hash": "2a274426da071df6fa95174501a31be0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\shared\\types.dart", "hash": "7e327134a49991d7ba65bbfe46bb8f4c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\window.dart", "hash": "b081e406a9e3448ff172ab7d21f31f7a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\math_expressions-2.7.0\\lib\\src\\expression.dart", "hash": "ee3f371a1558f84f3ebd2c536c048680"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\llms\\llms.dart", "hash": "d77c7dafddb482a50609c8db1929ba8a"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\user.dart", "hash": "f70d4e3a0a7bd13c3b0afcb9fc4ed868"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\readable_stream\\readable_stream_controller.dart", "hash": "a2dc682bba1a13719dabf4e3ee8f79d1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\json_path.dart", "hash": "34e2aeb93c3cfed39d556ec5421215de"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_embedding_response.dart", "hash": "b12a389dcde940668148aaca4534ee85"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_srgb.dart", "hash": "260defa43d3ab6d805cffffbd379859a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_web-0.9.4+2\\lib\\file_selector_web.dart", "hash": "c4417345673dd4ce2b9995509763255d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\fun\\standard\\match.dart", "hash": "c8852dee791e4f0f6e5368423a57f784"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart", "hash": "2ed82d0ee4672e99dcdec5652737899f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_step_completion_usage.dart", "hash": "fc0702cac3fc6cc383fa8b65b1f1c978"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\rxdart.dart", "hash": "6d2dba952020d690bfc0aaff3adbcd65"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_types\\rx_iterables\\rx_list.dart", "hash": "ab68330a48367bf72be12b0ab0d37729"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\lib\\url_launcher_web.dart", "hash": "3f6e143a371ae3ea26ccae00a723a057"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\library\\library_screen.dart", "hash": "f6da1b6e50f23e7b69e2269e92bc794d"}, {"path": "build\\web\\flutter_service_worker.js", "hash": "439d0a2f5d6a05519b409a9196b7add4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart", "hash": "f9aa2eb956d270d4df8b3a7cd5ac52d7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\_timeline_web.dart", "hash": "ffac69dfcb0e87c95808b0fb04680741"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart", "hash": "df6b7325835a6d1da457ca5056ab1860"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\background_generator_service.dart", "hash": "49ead343cef34cc40ada0fb3315ce070"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\pointerlock.dart", "hash": "292b2f9e18932510b27c2a138aa2c6df"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart", "hash": "ab1810add8d8548d1c5c5a550d988468"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart", "hash": "4e5ec1f160ded454a208f311a55b547b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus_platform_interface-3.4.0\\LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart", "hash": "a4474d2d39dd9373580879eb4bd18ab7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\src\\json_pointer_segment.dart", "hash": "ef6542434adc85279297515254085aaa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\routes\\observers\\route_observer.dart", "hash": "a43595711ec350c87a72c05413fde5a4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\requestidlecallback.dart", "hash": "4082f30e5cc474e4f38820b93f30ef3e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart", "hash": "fa98c8c6d036ea07d9ee49ea7d30f25c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\fetch.dart", "hash": "4ea1c9c28ce092fb1051ad1e954992ad"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_9.dart", "hash": "e7280a239aa04ba649fc950d8170cb3f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix2.dart", "hash": "ac51c125ed5881de5309794becbacc8b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\typed\\stream_subscription.dart", "hash": "63190b810e77cfebf3de760baaf59832"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\memory.dart", "hash": "767372d6b6d08250cb87bcdd1c3ccb7a"}, {"path": "build\\web\\canvaskit\\skwasm.js", "hash": "960d3a3b9eb3a670269c98efe3c443ed"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\chapter_detail\\chapter_detail_screen.dart", "hash": "82bb711301cb1a1828710f42339983d8"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\controllers\\novel_generation_controller.dart", "hash": "5c2ce83b4c876b3680a307fcadc09eb9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart", "hash": "a334501a734a440f60aa69ce87071ef1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\response\\response.dart", "hash": "464c3b3cb8dc0149df2d0a9f1e3ea137"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart", "hash": "ff0c28954cbb930913ed52a41f11189a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\mediacapture_transform.dart", "hash": "c7cf83a1db30abb62d2f6f9c10d30c91"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_debug_shaders.dart", "hash": "80e323d4c1ed63a9ca4160e52fb5a98c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\utils.dart", "hash": "7014dc257215cc17a58e5bf88855eff7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart", "hash": "47474102c009e7099f3a9bf1d7ea8e06"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart", "hash": "ccf5dc03f13f3e26baef323b1ff68d0b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\mappings.dart", "hash": "6052372e0d95fa60c75f1a43853482a8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\radio_theme.dart", "hash": "6383ecc859333da15aaf986d9897a7d6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\web_bluetooth.dart", "hash": "e29eca80b023da19b121fc3e372ca847"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart", "hash": "d932135a74692037b9bbbbb8fffffd5d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\delete_message_response.dart", "hash": "9352253870cc2b0343e9d1cc2f4cceb9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_message_tool_call.dart", "hash": "c334d8f02a3d653c89662af12f0dd4f8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\error\\tiktoken_error.dart", "hash": "5c75e2256c151fd49ac396779c06d29c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_web.dart", "hash": "9c524c2b6b1d64336fae713c811140f2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\url.dart", "hash": "03c1300d573d0b8d79399464a2d1bb8e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\credential_management.dart", "hash": "721ef479b7a4fcd21729b0acd4cb2669"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_compressed_texture_s3tc.dart", "hash": "1d64df0e3ebd5eb34fd94bbca3c3ff87"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\framework.dart", "hash": "3aa4cca9387aff45006999742e211fee"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\text_button.dart", "hash": "ae57ac152dc8bd45a57735cab6c0a634"}, {"path": "build\\web\\assets\\assets/fonts/NotoSerifSC-Regular.otf", "hash": "dad74d67a575308967abcd6b39544602"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\circle_border.dart", "hash": "8ad25d6c38ddc8ce7abb828b97f4571a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_stream_message_tool_call_chunk.dart", "hash": "5b6214b1319dd26f7e1a050c589e70b7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\lib\\model\\base_device_info.dart", "hash": "4f0e33807b3ea8a3a5d03a85dbdf565f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\backend\\storage_backend_memory.dart", "hash": "a8833e6afcfa9f667d78607fb38747ab"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\beautiful_soup.dart", "hash": "54bfea78a00a1f4b0366c3dcf777d9dc"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart", "hash": "abe3a12abba3d1b3a88ddb1ff43ea51e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\routes\\custom_transition.dart", "hash": "c104d9f577ac3b4a2340ff20c50b7a51"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\url_launcher.dart", "hash": "10bbfa83fe7c3c8f8a4964a3e96e5b58"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_closer.dart", "hash": "cbd0196f25d2f055736beb3052a00c19"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart", "hash": "dc8de4fb0846c2035cd04064058c8ef4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\src\\sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\semantics\\binding.dart", "hash": "55918e311f31842c3d4b2a092beb3eef"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\filter_chip.dart", "hash": "7cff3fd6b3f5da25b63942824d72a403"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\streams.dart", "hash": "08ebb996469240d7789e7d2ba9f08bc0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart", "hash": "bae66752808be6b5aaf6c0c266e9d40e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request_options\\fetch_options.dart", "hash": "3c9a8414ddf91c251b11d73aa184c209"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\routes\\route_middleware.dart", "hash": "08eb2a55100ffc0b18153acc15411963"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\chat_models\\models\\mappers.dart", "hash": "cbada2446070e8c3eb9e0dbca420f205"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\list_assistants_response.dart", "hash": "aa8ae1d977df0e9f658265ba71d321a4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\widgets\\common\\animated_button.dart", "hash": "10d031518b79d0742a89031d29e18768"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart", "hash": "40a8505ec0b7cd7676ab5efb486432bd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\transformers\\transformers.dart", "hash": "17a906017f55f8ebac92efe4c780282b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\where_not_null.dart", "hash": "9b84f667016de96aa99b12338a4dfb57"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart", "hash": "13dd9b1f72f655ece9886a13c5bd2018"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\_web_image_web.dart", "hash": "b283da1b259ea871815664517bc6e339"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\multipart\\form_data.dart", "hash": "fe23db3cc51eefba048d12654657902c"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\langchain\\prompts\\novel_prompt_templates_enhanced.dart", "hash": "272fd0ddf9f8830f65fa849ec686755e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart", "hash": "6c685d31c42c2f60c5082e01574011e4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart", "hash": "7a81afed71f41a7b99c7a3d0d1521ae0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\table_border.dart", "hash": "c58ff4d729abd2cae831be9ce43e48c4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_typed_om.dart", "hash": "a7dc7f54b0300393880ad5ea5e4db662"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\allowed_keys_helpers.dart", "hash": "41696c18e708950dccaf7cb4f5b7b195"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\utils\\separated_list.dart", "hash": "031cc72717282ff9f7f588e73fb0fd0b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\LICENSE", "hash": "80389552fd2fa0e6031f555c6ee7b983"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart", "hash": "c5df707bc55d571491bbfe69ab9e3329"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\foundation.dart", "hash": "f2027810282f79cfd11771b68f8bf50d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\encoding_parser.dart", "hash": "109eeb63e43422d207e9ad771c2ab623"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart", "hash": "40b9913c0aa23df5ee25ddd53480cbf1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\completion_choice.dart", "hash": "ba465d04a0b961ba9ec4b3bf0f926a70"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_chat_completion_request.dart", "hash": "e13ccfff113862630fa4488609b63992"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\prompts\\genre_prompts.dart", "hash": "330d0167f6d334c4ff61bbc4af6ee2fc"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile.dart", "hash": "d73c1749f82dbc0d4e3b6c07ae718382"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\quad.dart", "hash": "9a043d96e7ae40786de66219219bea4a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\sequence_selector.dart", "hash": "08a86a247a562fedccf05dbb8b5b6e51"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\http_client\\http_client_html.dart", "hash": "36ecb63aa2a77aaad66c731834428c06"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart", "hash": "9fda069b501810ac8093f3c0ad80e3f4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\constants.dart", "hash": "b5ba013c4a477fc9b8f10251aa24de09"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\knowledge_base_screen.dart", "hash": "dd0814fa5a224dbfe132fadde4923a78"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_storage-2.1.1\\lib\\src\\storage_impl.dart", "hash": "ab4718d08632b10b7018ee0cec4d78ff"}, {"path": "build\\web\\assets\\assets\\fonts\\NotoSerifSC-Regular.otf", "hash": "dad74d67a575308967abcd6b39544602"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\fun\\standard\\count.dart", "hash": "9be9e9ed2143e661e5de9381555fccef"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus_platform_interface-2.0.1\\lib\\package_info_data.dart", "hash": "e1c3e250099d4ee8d3419fe788ccb460"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\model_config.dart", "hash": "2a5e01c94f633de7d541051fe18658f8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\platform\\platform.dart", "hash": "95a0c03362c1a15ef15ebf67c442dbb3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\agents\\agents.dart", "hash": "0d88abd8853dda41adda1c89e00448cd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\http\\utils\\body_decoder.dart", "hash": "53a497a1958e26fff4ae83fcd05c15f5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\text_painter.dart", "hash": "73abcf271fb2de712f97decc836bdfe8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_cascade_6.dart", "hash": "1b34c2a0713b355a521927aabe0eb516"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart", "hash": "2c907a9b2d913f98aea742b5ead8d39c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\font_loader.dart", "hash": "59471e40595be9401faf6958667e9baf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\storage\\base.dart", "hash": "ccc17c2cce6f3d7fdf85f1fcfc2858c5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\get_navigation.dart", "hash": "678159d99d08e34c301c3280edc201f3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\debounce.dart", "hash": "1aea282ab07e82afe7a564125110e1fc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request_options\\request_init.dart", "hash": "8609a2c42df23f027ff9042f75074b53"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\_platform_web.dart", "hash": "0ad9be9d93ae5d28dda0c321dbf7af73"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_paint_api.dart", "hash": "79e2191a8641bdd80f9ff0de82ff35a2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart", "hash": "99fb2e65ec78997546349e740526b24c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart", "hash": "42e61179c9aef816ce39065a97feefac"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\interface\\tree_navigator.dart", "hash": "9df0dc8aca17b2432279c4bb292e3060"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart", "hash": "bf3b061fba244f7be1a407f9a282d92e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\connectable_stream.dart", "hash": "fcbda87916b8b2c4f7b88460ac9bb415"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart", "hash": "07230264b6ad4306fed087103930fd35"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\http_client\\http_client.dart", "hash": "2037342f49cae82377bb50e962ee2a30"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\system_chrome.dart", "hash": "87e89492f75fe81df0eb1d4896cf5d11"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\header_with_id_syntax.dart", "hash": "173f961c68374d113613dc6ee2e210e1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\range.dart", "hash": "a6e57cd7b87262b784eb2efe6875a329"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\qr-3.0.2\\LICENSE", "hash": "4076b6f7ff021f511a11282f13f0c61e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\src\\formatters\\float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\pages.dart", "hash": "14f45e5c920fa63ac0780017c9d2ca28"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart", "hash": "ada88e231d33ef7c2855cecc98e8c6a2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\conversation.dart", "hash": "62fc2c1f604210131e8c572e5c30b0e1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector-1.0.3\\lib\\file_selector.dart", "hash": "0488dd2bdbc807e5dc4ef8a7ffa47864"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\ray.dart", "hash": "5d9bdad87735a99fb4a503c5bee7c7fb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\retrieval_qa.dart", "hash": "dc1f2cf06f275854984f613087557308"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_source.dart", "hash": "bdd3a31817dfc052c506f3a7e2556fcb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\message_content_image_file.dart", "hash": "877c1f345e7032eb2338296c941b3d2e"}, {"path": "build\\web\\test.html", "hash": "5d9a9a682e70dec085a9245673dc57e2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\rx_flutter\\rx_notifier.dart", "hash": "3d375f301820dae753b399a615bb7b7c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\tree_printer.dart", "hash": "0b59ef1fb417d687f41af0202ba86cfb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\composite_subscription.dart", "hash": "76cc6c2b845bff11813d968688280b36"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_disjoint_timer_query.dart", "hash": "ec7ad138dbbbbb8da89674e3f9d8250b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\remote_playback.dart", "hash": "eda773e90fd6e46f7443712a481a89a2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_many.dart", "hash": "1f04f05279660e26d85fff2f5dfec4c3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\dom.dart", "hash": "7f54e5ba0047e40abc9ab825d4e1c116"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cli_util-0.4.2\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\announcement_service.dart", "hash": "3fb5f52bbb7ac3b54a00142bca190d03"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_disjoint_timer_query_webgl2.dart", "hash": "9596f92640ea1703dd10aaae0a28dde5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart", "hash": "eafa783f39fb35b9bb8581b697c3ba52"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart", "hash": "77c6bf950eb7c87baf89b95368f6acc6"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\DEPLOYMENT.md", "hash": "465c4a09fda200f469cf467658f8e233"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\cancelable_operation.dart", "hash": "57ef1f2eff2168c2e2ba1c3e4e60e05a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart", "hash": "4538b5225c156e895c5d603660cb1358"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\token.dart", "hash": "f81e0f51e6529eaf92d4e8d6196e4e13"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\agents\\models\\models.dart", "hash": "a6731f9779ba4fab60148e2857c7aec2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart", "hash": "de392e4c7043eeb731474c52267542d5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart", "hash": "c64b32c068f0d8679ed2b8d699659780"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\performance_timeline.dart", "hash": "3ee923a2e66258d09bacdd2223e9dc29"}, {"path": "build\\web\\canvaskit\\canvaskit.js.symbols", "hash": "68eb703b9a609baef8ee0e413b442f33"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector-1.0.3\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart", "hash": "c2e8ba48563eaf38bd3b50761b60e973"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audio_session-0.1.25\\lib\\src\\android.dart", "hash": "********************************"}, {"path": "build\\web\\assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "hash": "33b7d9392238c04c131b6ce224e13711"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\layer.dart", "hash": "0a66b5bb14fdbf88558f88b07b5c6f31"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\readable_stream\\readable_stream_source_controller_method.dart", "hash": "afab9b227916349fbd147a5dd1ce0db6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\dot_name.dart", "hash": "13bc13ebe83cde910aeb144e52499541"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\link_syntax.dart", "hash": "925d16251557ee51dc530d624a9cecaa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_instance\\src\\bindings_interface.dart", "hash": "da6bbbfd63c878e46f3efb23b268aed5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\strut_style.dart", "hash": "1d7927056badbebd8f79686e3ee05ffc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\slice_indices.dart", "hash": "16e481e6f0614d4b4c39ce85dda63377"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_iterator.dart", "hash": "6228294dfd70c5fd32f84a068fbbb04d"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\index.html", "hash": "3d0366a7d297e1535af7518ee6397f3e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\subscription.dart", "hash": "8ab19033cc6a918c1e4f454495a9ab5f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\fun\\fun_factory.dart", "hash": "d3212766228f1d004bc726e78b0c96c1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_generator-2.0.1\\LICENSE", "hash": "4329bcdd1ac50446158359963f9d3403"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_types\\rx_core\\rx_impl.dart", "hash": "3badcb87fefe5eaa50dfd1735265e5b7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\geolocation.dart", "hash": "fd88a6bfed6b081f6305e8f99c178be0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\overlay.dart", "hash": "5f23b60e87a7fcb270f62570673a16d8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\chat_models\\utils.dart", "hash": "1286b05eb31eb78d571875d7e2df954a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_markdown-0.7.7+1\\lib\\src\\_functions_web.dart", "hash": "d8f5298a1e365f3c09d6b96e85e00e6d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\accelerometer.dart", "hash": "0436795f780c587c284e98075ee5344d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\oes_standard_derivatives.dart", "hash": "44676c94663b8ff333fb9104b594ea02"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_shader_texture_lod.dart", "hash": "74d1e8a2fbc012cc4c5589defc75f038"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\lib\\src\\mime_type.dart", "hash": "f5e211d8beeb6fe549de90fbc48a4a35"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart", "hash": "7b9a23372e88dcc5453cc21b5ea3f89e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\any_of.dart", "hash": "8cd59827d2f99e2d6c62f2f38c275cf5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\utils\\network_test.dart", "hash": "32e07ee8c6d31641725286237e78d471"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart", "hash": "4cf81f473e6af6b63729d72f41fe712e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\expression\\utils.dart", "hash": "8608f71f077e370ee14d37c711e6580e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_color_buffer_half_float.dart", "hash": "74bc91ac0e2a797930d6f45776b0915c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\transformers\\base.dart", "hash": "c72fed6fa289775b17a123ce8b561750"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart", "hash": "3199c921467a961819b9674fa5fcefe4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart", "hash": "4205ba2309c32163616dd63f6264de11"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom.dart", "hash": "ceb8e4633e0ceeb9e91c96c160ca4bf5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\key.dart", "hash": "d3c9b4f0e500f74ef525ca325c595e66"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\vector_stores\\memory.dart", "hash": "6f2720c02d7a688cc7db9f86da9f041a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\value.dart", "hash": "bf3aeab9379cee97ddcc69d885a477f5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\routes\\get_transition_mixin.dart", "hash": "af837d2617385fce42940f99ba374b33"}, {"path": "build\\web\\canvaskit\\skwasm.wasm", "hash": "f0dfd99007f989368db17c9abeed5a49"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32_registry-1.1.5\\LICENSE", "hash": "1eadf5be3116dc31e5f04d4d6d352497"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\strings.dart", "hash": "334ba26bb4c80bc858b59784197e1e80"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\platform.dart", "hash": "235a9cbd9dfd2869ed39062656c7e07b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\request\\request.dart", "hash": "af3069b2b91f38050133afd4e0c1c289"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\stores\\base.dart", "hash": "5c0f0ad7281bf32ab70dbca8ebc1eb40"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\context_extensions.dart", "hash": "a58f59b185ebab94d3c12b3dec1b1487"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\src\\_internal\\array_index.dart", "hash": "95ab6d8405ae4d29126fa54a1cd7075b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus_platform_interface-2.0.1\\LICENSE", "hash": "fdd76c3a4ddf2b68daba4913b8a68e0b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "D:\\element\\flutter\\packages\\flutter_web_plugins\\lib\\src\\plugin_registry.dart", "hash": "642a8c07a93aa2af11d1d6f523ea3bd7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\screen_wake_lock.dart", "hash": "02b2fa04e8c4cd7b45c9b4e3d477e339"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\future.dart", "hash": "18c04a8f8132af2c1b1de5af6909025c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart", "hash": "4d7ff70b73cfe04b14f559266b5a991e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\parser.dart", "hash": "505fb0828e4fe58e1a49ddab272d7b71"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_client-1.1.4\\lib\\fetch_client.dart", "hash": "3e10d628740fec66edc8dfce72546622"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request\\request_body.dart", "hash": "994839959db5351afb55ac2f497aff8e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_step_details.dart", "hash": "a2683fe0be0094abd86fda9a55fa509d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus_platform_interface-2.0.1\\lib\\method_channel_package_info.dart", "hash": "a66386956992c16f4103460902219ee3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\_isolates_web.dart", "hash": "57ec711550e289b2e478747a94b2443a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart", "hash": "e2389f2925d99315a85f2e8494b95d95"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart", "hash": "e1309fdfc73f3921dc1d2b212d57d217"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\fileapi.dart", "hash": "c41c291723be3c63d244abf8b69156c6"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\chat_history_list_screen.dart", "hash": "2287ab92e10a25a5cec8c5f241e242e6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_type_error.dart", "hash": "87869abe3eba2d869bfa48990f86f2cf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\sequence.dart", "hash": "0b1a431f52b54788ec3e9b6da7d87909"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart", "hash": "970af3dcb7a44930e489ee7e8deb4742"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart", "hash": "34d5859bd13d7c941916846f41ef3b31"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\helpers.dart", "hash": "41d7973540c4a5440409f53a1eb042cb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_chat_completion_stream_response.dart", "hash": "2c2ee5409ecff356e73f99b93a839d01"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\tts\\tts_screen.dart", "hash": "01f6fc015515b31db8b249fb17b43399"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webaudio.dart", "hash": "c9f9523e7096a2ab94085888a12cd9be"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart", "hash": "fe77607015d95f333be939b0b39afbfe"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\core.dart", "hash": "b969cd0066fa07b8082edb76d2af77e1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart", "hash": "9e82eaf3b187151c25ee1fd62e6842b6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\cssom_view.dart", "hash": "a6df205ba9fd0ce49f7d0884d1f02b33"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\matches.dart", "hash": "3459a8a962662899e6d1ed009af8ba45"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart", "hash": "ea3606b05aba02b7c9700df8f40a209d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart", "hash": "6e144abc5846b7e5eda10cec79c9051a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\tokenizer_base.dart", "hash": "e3bb2a25791065817d184fabfb8f7d0c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\lib\\src\\intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\and.dart", "hash": "1e9ed9cdf00b9449d9b72dcd00add4d3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf_web_socket-2.0.1\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_compressed_texture_s3tc_srgb.dart", "hash": "475963783287cfaf98b88b0438997e21"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\helpers\\cross_origin.dart", "hash": "c63cb9a1cdec2c4ed2b466377b08b694"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\runnable\\input_getter.dart", "hash": "a33f9ee3d40138d768dfd02712796e45"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\langchain_openai.dart", "hash": "f6ac169895f4f7819402aaa1830917aa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_message_content_part.dart", "hash": "3ad257e8cdd6cf1fafe255a470ce0257"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\choice.dart", "hash": "253b43ba9075c77f9ce5267d91880da6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_masking.dart", "hash": "2e81446170dfbba4057d307bf888d364"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart", "hash": "4649019d5a0dc7b0fcd9206c2ef4892e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\helpers\\enums.dart", "hash": "b6cfd47ac7d8e231ddfcacefa676b749"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\bs_soup.dart", "hash": "6ec6655bb7ee5115fbf4752119fa7d6d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\whitespace.dart", "hash": "f0df878443ef28db864b73e66f8206a2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\list_threads_response.dart", "hash": "9abf6f23708f8ceb1112f41b16c460c0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus-9.1.2\\LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart", "hash": "ac7068865efbe78773199ef71b213440"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\embeddings\\embeddings.dart", "hash": "d827f6274cd0f81eb369542a7f098ab3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\pattern\\parser_pattern.dart", "hash": "79a5f25a1a9d4aa4689bf37171e1b615"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "build\\web\\assets\\AssetManifest.json", "hash": "95841f991a8f3e772533817a80b8b05e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\compare.dart", "hash": "38dd18472a5020b2f84af889ec1bd67f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart", "hash": "cde6a145081704bf14d1097f7ddb58de"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\shadows.dart", "hash": "dafc76ada4dc3b9bc9a365a7da786797"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\src\\complex_converter.dart", "hash": "e151d16159ae628c1962f60528b3bb98"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\writing_style_package.g.dart", "hash": "fe9791e9b5e125eabff68168f6a29343"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\basic_types.dart", "hash": "3d2fd6ef375dfede78f67c892a9e3f93"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\web_otp.dart", "hash": "29f075236669305716fe4d5d86d72403"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\capture_sink.dart", "hash": "7c57a9163e2c905ac90a6616e117766f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\unordered_list_syntax.dart", "hash": "70eea0e75010989ebaa8109fb0c991b2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_web-0.9.4+2\\lib\\src\\dom_helper.dart", "hash": "ccb87ebabdb705bcfec3224e687488ee"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\adapters\\chapter_adapter.dart", "hash": "e806c31364604e569f0c9d55390f1b33"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\controllers\\writing_style_package_controller.dart", "hash": "2335ec61ce735d64e4d2857dbc7a0918"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart", "hash": "31fac5b5882f19d242724b858b97d228"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\tweens.dart", "hash": "f9cf43c94d23a1e1093f4f1cfd789d18"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\trust_token_api.dart", "hash": "25c47fc47f8f474488e3d0c9f9806cef"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_client-1.1.4\\lib\\src\\on_done.dart", "hash": "6ca905a64ce85d1e4f7f18a89f796704"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart", "hash": "b649f669a0c7234ba97f959808a59864"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\windows_update_service.dart", "hash": "32a9a51526b429a3090b90bcc7fc8bf9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\settings_screen.dart", "hash": "209b66652d31d8a6b8b7175c781bf761"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\enum_helpers.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\chat_models\\openai.dart", "hash": "2a58009a307b06ddbc42643728fb6ef4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\constants.dart", "hash": "9a1952c60cdeb2917867cc0e3cea5391"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\web.dart", "hash": "a197368e3a1ab8d998fc7b68d65ff02f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dart_style-2.2.4\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\character_generator_service.dart", "hash": "c24e9f97b4a9b1299aab78b44c048e43"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart", "hash": "8b74887a94f1e7ef2e052092f1fb2b30"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\runnable\\base.dart", "hash": "71a33ef17b59aeccd0a34dbffa9cee7b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\combine_documents\\reduce.dart", "hash": "6198025051d8ab4ce69c51f24e3098dc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\http.dart", "hash": "85dbe788da3bd7c9253cbae3649069b7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\typography.dart", "hash": "9d159d68dd31270a9eb37ce4fb3babd7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\embeddings\\base.dart", "hash": "65b09dd9c1ea31095b77f8febb721547"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_zip.dart", "hash": "1dac993c7444b99a17f2dcf45acaca97"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\geometry.dart", "hash": "1f69b6ff45adef5847a6ab5120852a5e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button.dart", "hash": "064436cb7f327e308f036ef9b5c40b04"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\thread_object.dart", "hash": "e119180b5ae31236995be6dd5f978d1f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\src\\typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart", "hash": "f3ce35c15285bb22d0a813b27365491a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\modify_assistant_request.dart", "hash": "6f2cbd4354c51f2863003cd8939be4f7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart", "hash": "2a36080f4719a0b4485bff0582d9894b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\language_models\\base.dart", "hash": "29d48c4e3d67766a120c08caa8f6eaff"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\base.dart", "hash": "9a23d39ae828cdb3bd7ff54ec059998b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\extensions.dart", "hash": "aaca7738852c8a8941180d5cac079df3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\readable_stream\\readable_stream_source.dart", "hash": "642ec27e2f8eb7f5266436e60760d045"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\defer.dart", "hash": "d06420fd88fb8f7cc3acc1643051178a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\actions.dart", "hash": "b438b92d95aa74f212a0c40b725e10ba"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\radio.dart", "hash": "d943510867769f5dbb0919992d94abf7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\converter.dart", "hash": "24dd1f01d0ce298347e47fd60702007c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\langchain.dart", "hash": "f07156d97c34eab132d1c02cac14f898"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\generation_mode.dart", "hash": "ca0bcab79ee8b035dd9b5076579376e1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\simple\\get_responsive.dart", "hash": "7f5bc802c84ae0805543441def93149a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart", "hash": "3a20b4d301e83ddf2a700e43c3d13f19"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_message.dart", "hash": "99fe1dca110d0104dc53190c264f17ca"}, {"path": "build\\web\\assets\\shaders\\ink_sparkle.frag", "hash": "ecc85a2e95f5e9f53123dcaf8cb9b6ce"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\build\\web\\assets\\AssetManifest.bin.json", "hash": "99914b932bd37a50b983c5e7c90ae93b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\core\\token.dart", "hash": "595737cf044c5d483e4615a1b0e1db71"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\helpers\\events\\events.dart", "hash": "61a9113d5f96e171950654b239f000d4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\rendering.dart", "hash": "31b6e401282dccfbeae67ee82469cbf6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\src\\formatters\\Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\vector_stores\\base.dart", "hash": "94cb0a83c7ae01cf3a09663be2fd8053"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart", "hash": "7498ab451e1ca95d81055ac4e3a25079"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\binding.dart", "hash": "e0fceafe591ad5fd70a41190dd121f08"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\graphs-2.3.2\\LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart", "hash": "30e5fccf0da79e15eb5c9e94d97c6489"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_finish_reason.dart", "hash": "5f1eef668c63fbeb264b50d319862f4a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\messages.dart", "hash": "31e2179466decb4da4d2ae1e51938a51"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\character_card_edit_screen.dart", "hash": "d00dfb21421fe4692cd552a5e2fba7a5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_animations_2.dart", "hash": "f56db1857dbcbb843dd89b7f55db0815"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_view_transitions.dart", "hash": "ae2402018a3f515ea615acc40c8769e5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\border_radius.dart", "hash": "2898250a220591be8181ac7caeb0a684"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart", "hash": "58024a76590b0e38955acf1b045a1a00"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\question_answering\\stuff.dart", "hash": "e88005811b52e7058ff48fe65ac303c7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\nav2\\get_nav_config.dart", "hash": "50e73a29b61bc883d1bd71c1c43392a8"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\langchain\\services\\lightweight_generation_service.dart", "hash": "761503da2f65f0686cdae226346be793"}, {"path": "build\\web\\web.config", "hash": "9dfa126e952b6dd00c19003ad2e39854"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webrtc.dart", "hash": "287e157d179a7159895d685607ff445f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\autolink_syntax.dart", "hash": "c2ebbfebc49dfcc9471b0c2d65387378"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\image.dart", "hash": "1c6f4bde2f41b6d50498e3026f24dd1a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_parser.dart", "hash": "e9187289948b9974fe1da61ba7af861f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\snackbar\\snackbar_controller.dart", "hash": "657d0c151cd15a2cea4a2f56b98eeab5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\export_service.dart", "hash": "c3b62da26bfdfafd025aa3e5241bcb85"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_message_role.dart", "hash": "99d930f0cceb62a18c633d7030e72bc8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\maybe_just_nothing-0.5.3\\lib\\src\\maybe.dart", "hash": "38f0397c22e0e23066c6010baa6b1dbf"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\carousel.dart", "hash": "101bacb7a4e855643a702c63355b2f7c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\skip_last.dart", "hash": "355616c9fb00a5e0ec803fffa8f33eff"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\list_messages_response.dart", "hash": "19e73882ba25c8b6268d97f321e5760e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\stream.dart", "hash": "809f1f0bbe7ee77e69f003952a5525d5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\triangle.dart", "hash": "e3f9a51488bca91a3350831c8ad6722f"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\langchain\\chains\\novel_generation_chain.dart", "hash": "04f15acdc0fe047c44485ed2220e22c8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\scaffold.dart", "hash": "f0092135c4cff7e4933b157a9d09ce9a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\modify_run_request.dart", "hash": "3cf55b3ad4cef969af99067d9380b063"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_web.dart", "hash": "ee05dc1cb0642fe18d0b49ea89149397"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\slider_theme.dart", "hash": "5955a8a2378ff31186792d455357c96f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\selection_api.dart", "hash": "ef86635f28c74edbf20990a9c867ebbb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\private_network_access.dart", "hash": "7cf0d50888c845f6bc217f8c2f6e3826"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_message_content_part_type.dart", "hash": "f1e5d26e8837e758a6f26ba298751bd7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\none_of.dart", "hash": "2080f99186cef2d2ec3f4c6c5b7c768b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\crypto\\aes_cbc_pkcs7.dart", "hash": "93042b4972c8255fa75112f440f77aea"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\position.dart", "hash": "faedea5895c9ddd2b2c270817c61d1f4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\image_provider.dart", "hash": "7975814e8804652eda7f8b726d3913b2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\orientation_event.dart", "hash": "00ce625f6c9a3d5b0cd196994fdbaa0f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\collection_extensions.dart", "hash": "874c21db82e74ec1d570b48ffb1bad17"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\string_stack.dart", "hash": "aa27dfc54687394062db977707839be5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart", "hash": "933d793ffbf8f34eeb5f26962e83590f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart", "hash": "ecb57271d69343712d88a50442f2e8ed"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_completion_response.dart", "hash": "ced31e8cea8cf2f92331d41b8066fbb6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart", "hash": "1723ae3582276265ebad2922945dbeb2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\intersection_observer.dart", "hash": "819fcc538d96464923b4d6c08b2bec29"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\abort_controller.dart", "hash": "09291cae10281c53f23dc680a7d97eaf"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart", "hash": "f0f22cf747d09b92d955e41b12852d3c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\back_button.dart", "hash": "18939fc6e74f9b391a45ae341fe30128"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\stack.dart", "hash": "6169ff2b33fd5e84357f9296b4bca8a7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl_helpers.dart", "hash": "035f884069d09d77af2d64ffa2bb28d4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audio_session-0.1.25\\lib\\src\\core.dart", "hash": "f3c5cbf9ea9b5cf496bac755beda2220"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\_capabilities_web.dart", "hash": "08f387cbb81580065ce0db15cdeb9bff"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\accept.dart", "hash": "740f17823564c3c7eca15bca5c110e17"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\bidi_formatter.dart", "hash": "5c81dd07124ccc849c310595d9cfe5be"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\maybe_just_nothing-0.5.3\\lib\\maybe_just_nothing.dart", "hash": "5bfbeddf3c4b706928d554131a342275"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\parser.dart", "hash": "e4d5eb474812b6fb78ddb16f9ddb9472"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\material_state.dart", "hash": "29dc810717daabf9c43e0c29a9535662"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\selector.dart", "hash": "87e738dd98a4b149949b29a22150ed77"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\force_press.dart", "hash": "482df57db20848c7bbf606b03e258a69"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\spell_check.dart", "hash": "16b7d416940fc53eca969b344627767f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart", "hash": "99712ab0098106c505b424f8542edeca"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\table.dart", "hash": "cb6fd956322c346aa345e6cfbe17c708"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart", "hash": "90f2bfd8a277caf2dfdda31d1db65bf6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\certificates\\certificates.dart", "hash": "e32009ec52ec16529ba4be790c383002"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\lib\\src\\utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\pattern\\pattern_iterable.dart", "hash": "f0ae0acd94eb48615e14f6c4d1f5b8e0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\LICENSE", "hash": "9633ac2bb6bd16fe5066b9905b6f0d1c"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\chat_history_service.dart", "hash": "60c4994766bad0f65173e6be5b22d585"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_transitions.dart", "hash": "709e5921e8c605c3418942ca3def0869"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\agents\\tools\\tools.dart", "hash": "fa03915d35afacbf12a6830c5c4b92ee"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\watcher-1.1.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\map_to.dart", "hash": "a28073e1b0a1ffd4999c24379f1dfe02"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart", "hash": "4faf3d2a26408c96acccabef79b5080c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\svg.dart", "hash": "8cd036f452e07f77feeb099c5ca20538"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\button_theme.dart", "hash": "b74031010357e2c6c8e502ed635f6d87"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart", "hash": "f1be26fd7d1129f7c28d979d72027718"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\LICENSE", "hash": "6f788d685f32e6ff7adde4ad5af81d5a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_moderation_response.dart", "hash": "4e80741eb11d1f8bd0f34e1e22931d1d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pool-1.5.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "build\\web\\assets\\assets/images/wechat_pay.png.jpg", "hash": "d7e79fbda9bba9aa63a01c6279feb532"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\about.dart", "hash": "69562fbf89b2450cf68b181723b06085"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\gradient.dart", "hash": "03eb0db4ace470b8834f6330a9473b70"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\typed_stream_transformer.dart", "hash": "991902b33f1d81c417b707a41341ed59"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\utils\\sequential.dart", "hash": "b5519514c9b9570c951c0da186030e29"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request_options.dart", "hash": "1ac2b5c746ef1c130659576a0d32a393"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\langchain\\services\\novel_generation_service.dart", "hash": "7952406926256d083f7aec3564eb5ef3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\alert_block_syntax.dart", "hash": "061a3d335627e2f9802df260cb8a4433"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\min_max.dart", "hash": "50c5f00339854085c2f637109c4166f3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\eof.dart", "hash": "6a083480a6cb878f98927a9271454bd0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart", "hash": "e495ad45e6acd5e92563ab6f2ab1e68c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\lib\\src\\share_plus_web.dart", "hash": "8c2447d5dd0dc7741e403fb6e607287e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver.dart", "hash": "fd6e38dfcca3f82f8df8f49176ae85d4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\compact_number_format.dart", "hash": "9068f4d63af1ec44245b76b7ab4dfa48"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\queue\\get_queue.dart", "hash": "84306e16ae8313bd8d3bc39a2e047582"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\adapters\\novel_adapter.dart", "hash": "803c1eec7a64aadb5ba5ceb2d4de2067"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\crypto\\hive_aes_cipher.dart", "hash": "69a68782431189a163d7031587f20438"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\csv.dart", "hash": "026cd45f1ace89d6fb0fb38be88fc72b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\maybe_just_nothing-0.5.3\\LICENSE", "hash": "d6f495acc81279a6ec5c77dc3d66647c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_texture_filter_anisotropic.dart", "hash": "0ed231bf9417c36ac7feb2ebd972b015"}, {"path": "build\\web\\build\\web\\icons\\Icon-192.png", "hash": "99914b932bd37a50b983c5e7c90ae93b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart", "hash": "d99d22e8a62a3ce1fa8b04b21702e1f6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_2.dart", "hash": "dc92a928880163bbe0232a641f7f4276"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\predicate.dart", "hash": "9d95e55b0ed6080e677989c4e1e1cff6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\repeat.dart", "hash": "57ef315bc7f35da7e489915ef8572118"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\prompts\\pipeline.dart", "hash": "334f2d3979c4981870654eda532af91b"}, {"path": "build\\web\\welcome_simple.html", "hash": "513f88ab0f6cf5ebb2fc088d8ac9243b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_flutter-1.1.0\\lib\\src\\hive_extensions.dart", "hash": "3a5e5ce96980d4eeb6ef4992080817d5"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\embedding_service.dart", "hash": "52f77e5dd1e62e396a0a826b6220358b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart", "hash": "a4c1cab2135ba3ea8d1385e894ed0d09"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\radio.dart", "hash": "86cac5e304f32a8cd937f64fee31ec7b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\header_syntax.dart", "hash": "cf123292a83ca8ece830868410b3a18a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\treebuilder.dart", "hash": "2c8ef2ed22dd79552a4d286b31817a27"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\event_loop_extensions.dart", "hash": "2a8d86652b4f2c09ce24d11ebcda676c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_stream_message_function_call.dart", "hash": "8aca8d17f50783e3fa9bde9bb4050379"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_thread_request.dart", "hash": "18596f8b94f7905c2664410193a8078d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\semantics.dart", "hash": "a9d4e4b3f6357c540f77101737a25e4e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\dynamic_extensions.dart", "hash": "b68b93046d7cf1647390fe24320d7939"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\where_type.dart", "hash": "93f43c6a287e8cd98477a02e6aa0da8d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_token_logprob.dart", "hash": "301bb34c6cfdb83f1d423e9fc4666740"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\welcome.html", "hash": "77373ec2ddeae5c82a94586196adfe14"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\object.dart", "hash": "b614d8172098403c683c68aafa3e92e8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\fun\\standard\\string_matcher.dart", "hash": "94d44fb80613aef7449f6f02dffd8fa0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\inline_html_syntax.dart", "hash": "0674eb8c5b95e50e092f56161d2e86b4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_frag_depth.dart", "hash": "d02fb3624a4fb2e006c88c8f598e3daf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\fs.dart", "hash": "8793ac2a7158951b613820f6a44dd355"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_conditional_5.dart", "hash": "6e9e644f0613d2701339b82c7dbe6f4e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart", "hash": "fb5f7eb90b6c4b891e8a1cda244c0a30"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart", "hash": "bfd7a00b9fef90dbf3b6d14f6a2f2901"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\assets\\case_folding.dart", "hash": "07c0032c531fd95c8f64342b0fe7fe73"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart", "hash": "9621a4c337d24a926ff50126ce88d7fe"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\token_kind.dart", "hash": "4b721bbf0c0f68e346e09e254b6b8d5a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\race.dart", "hash": "2164e0e3bc00d90bd03708ddfd475ad9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\decode_html_syntax.dart", "hash": "c87ce90134f51a65353452c7a87a6f89"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_texture_compression_bptc.dart", "hash": "c5759bd6693e3553630b0e87e474e133"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\character_type.dart", "hash": "bf165597ec9e985e7c66fef3d908a791"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\agents\\tools\\base.dart", "hash": "b021fa8d4de88e00d4568fd1fa31d5ce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\export.dart", "hash": "6f556f186aa53cc00ad944512e5618e2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\runnable\\extensions.dart", "hash": "92a7433902ce64a399a370455b09ddc3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_resolvers-2.0.10\\LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart", "hash": "f0bf47fdaf6f5a5be43f49d2d73a3145"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart", "hash": "f098cc52d86fa817137fbc0f85c41967"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\welcome_simple.html", "hash": "513f88ab0f6cf5ebb2fc088d8ac9243b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\routes.dart", "hash": "9cd3631ea498823f8c3d9beb9b005195"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart", "hash": "451a797396c48722517ac4fca1ec1c62"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\exceptions\\exceptions.dart", "hash": "c2337a7304df78f9d6a1b2c5aebd40a1"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\favicon.png", "hash": "5dcef449791fa27946b3d35ad8803796"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box\\box.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_config-1.1.2\\LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\borders.dart", "hash": "fa7146d472a712331eef3a404e2fabda"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart", "hash": "61d6754850dbffcf4687388b630652ad"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\merge.dart", "hash": "c2b88768bdc9704848019fd9df8c2546"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\message_content_text.dart", "hash": "2a0d9242579044fc2a69c2f63fb57034"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\router.dart", "hash": "e4d4fcce981dab2d3fb4a6a5532e2e78"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\math_expressions-2.7.0\\lib\\src\\parser_petit.dart", "hash": "20a5efbdd92f2adfb2a1d2d8b69d77c4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\grammar.dart", "hash": "467b2b8993363b1b27f034f6c1cca476"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\type_conversion.dart", "hash": "032c93433e86ca78b8bb93e654c620e8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\deferred_component.dart", "hash": "f20b8958b0c35e9bbea75a43c9cf0e59"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\polyfill.dart", "hash": "bc0eb13caa9c0425831f18962dfe12ef"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_queue.dart", "hash": "cf0f2c674cec774d8fc0990ee818316f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\scheduler.dart", "hash": "3ac176a9235973980af3b75bd0c237ff"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart", "hash": "6249a6424953a3bf92990a55f2960d68"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\emoji_syntax.dart", "hash": "3e8b98b26515fbada3b14f6c873faf05"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\pages\\chat_history_page.dart", "hash": "e643d4693b7980507687fe1ecdee6438"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\banner.dart", "hash": "4db88ed53fb502c2c73cf2554abaf766"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\LICENSE", "hash": "4c5a88901110f96f096d0a05cc607301"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\box.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\strikethrough_syntax.dart", "hash": "a53739ca5d1d54a324c315609f0f2430"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart", "hash": "b149267c83ef4fca8800213bc7331992"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webrtc_encoded_transform.dart", "hash": "c070aa3ca91b493eadd482d443fbd762"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\cache_service.dart", "hash": "c3d3453cb61d276b1ff8d9508fa5196c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\maybe_just_nothing-0.5.3\\lib\\src\\just.dart", "hash": "b27557aeba800dbbb47c93bee3f14092"}, {"path": "D:\\element\\flutter\\packages\\flutter_web_plugins\\lib\\flutter_web_plugins.dart", "hash": "df42230a948949467a13a0b57c8f748e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\backend\\storage_backend.dart", "hash": "60a867309ff4891239672ceeb021e4b5"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\.htaccess", "hash": "c41eb092550261ea39b88529e3a7aee3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\backpressure.dart", "hash": "33135edc8fab501ab562a08b8322d832"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\fun\\standard\\search.dart", "hash": "7274d42499d36e5c259158d7206a73a2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_splitter.dart", "hash": "698b7b5743b9cfa0aa9d08de156d04b6"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\icons\\Icon-512.png", "hash": "66573d37ae61532f6666a2496c10fcb9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\_fe_analyzer_shared-47.0.0\\LICENSE", "hash": "fde2b1b7d744e3606529be50acb7fded"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\xhr.dart", "hash": "4efd485a39c822e8c66062c390eacf7b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\ai_outline_parser_service.dart", "hash": "51be1836d38e52da77292645718e903c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_markdown-0.7.7+1\\lib\\src\\builder.dart", "hash": "e23f882f64ec88f1f3060991a527ea50"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\object\\hive_collection_mixin.dart", "hash": "3acf14588aeccbac8c5d9e50e5db9edb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart", "hash": "bc19869e91597ad295ed0aa82807d433"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\scale.dart", "hash": "feb63d6dacc13b5039af9cf89c4164e4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\page.dart", "hash": "541afe70ddb63143404db82da1f4f6c2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart", "hash": "19ca41c14be0e05637a511d945b2f810"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\possessive.dart", "hash": "08e17247b131fb75466c336e9a11fcfe"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart", "hash": "4d5c4ddb1e85a31eafbdc436888543cd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\nav2\\router_outlet.dart", "hash": "e823d632045db438492d60b7ea1fae02"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\badge.dart", "hash": "1d183dd2fa481536757ae61cc77ed9da"}, {"path": "build\\web\\flutter_backup.js", "hash": "e32f874a0fff1dd87a200aa8ac22f487"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\io_client.dart", "hash": "278e31e5169963c0c3ea3ee9b4bb4469"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\ast.dart", "hash": "966aceaf383aa3c1ed0b0175138467b1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_submit_tool_output.dart", "hash": "5a9b6c3b4a48023ffa956af0ee415cc1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\reporting.dart", "hash": "41097783dd4318deeac7be3e96677833"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart", "hash": "51069c14005cc63df73f7c8db5520f31"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_config-2.2.0\\LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\chains\\qa_with_structure.dart", "hash": "a6c9572061acad21be4c68873593a02b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\pattern\\parser_match.dart", "hash": "d742d41268dec3da5e669142ae344928"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\dummy_block_syntax.dart", "hash": "e0ce098cb023b3f78650560484267022"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker.dart", "hash": "7834be5adb2c9fbcf1c4d9c5ea1c97df"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\assets\\html_entities.dart", "hash": "045c0644f44db7ee4fd7bf6e3432a654"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\crypto\\aes_engine.dart", "hash": "be8db0f0d8f9d7aef0bc2cb469f73907"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\types\\file_save_location.dart", "hash": "3c21d269eae774b7e06b8adbe73aa18e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\method_channel\\method_channel_permission_handler.dart", "hash": "219013e8bbe8cf19fde2d3520067ba39"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "d2232e4bd0457a4a3eb6bd897846eb44"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\number.dart", "hash": "fb78524a71154ebb68f864455e092e8d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\no_splash.dart", "hash": "363dc40bd108240cb513878e107a260d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\prompts\\base_chat_message_prompt.dart", "hash": "1ab3a2fcf755f62bdc93d2e9405a1b4c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\output_parsers\\models\\models.dart", "hash": "195467b78d76f5a06502d162fd8277fe"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart", "hash": "d51f921f8bdc4964957a62b4a42a99e2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\link_parser.dart", "hash": "c5f8da76fb2d486522bf03c0b49f1bbc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_6.dart", "hash": "7eaf5b7e19afccfa1bde4bf16bf53648"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart", "hash": "fed4a3dd4a4955f717b4dfd587484ab3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\debug.dart", "hash": "c15b0c06c9817813063ea4c1023d57fa"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip.dart", "hash": "ba0b6872fcc8c038ed2dbe8a8b7465b2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\dom_parsing.dart", "hash": "723a3d6fbd3de1ca1e39b70c5ddb5bcb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box_collection\\box_collection_stub.dart", "hash": "********************************"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\curves.dart", "hash": "2fac118c3201a4bac960d7430ddda0c6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\agents\\executors.dart", "hash": "ce8f88e79f32d043783dd54e4142abcb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_client-1.1.4\\lib\\src\\redirect_policy.dart", "hash": "8a612eb8c428fb8e627b384c25856eaf"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\slider.dart", "hash": "f0fdc3dcbed6a67203aefc93ab718392"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart", "hash": "beb0225376869e0c92a19831596494dd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\html.dart", "hash": "75bb30a58c7ea909b421ab34f056fdbf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug.dart", "hash": "36b7c748941e821dea3c3c964032ac12"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\network_test_screen.dart", "hash": "faa918b81e235ea7554c4dd13eafd7be"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration.dart", "hash": "e22a38dff2e5b45230581797ecf557e1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_completion_usage.dart", "hash": "f53ce2decd0605fbc481fb070d930079"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\combine_documents\\stuff.dart", "hash": "12e8fffd672c878a162d66472d557735"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\embedding_usage.dart", "hash": "da6fc4d9d08c8f1f6d18ee7853175572"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart", "hash": "671687d438b0aeebd40ca6362edb00e3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\error_helpers.dart", "hash": "73c0a59e2d19aea71c6029f871aa9f67"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\assistant_file_object.dart", "hash": "d9f1848128ac1863e0fcf8782cd704a7"}, {"path": "build\\web\\version.json", "hash": "cb3b4f8ed94f921ad46f9b2f62fa2a85"}, {"path": "build\\web\\assets\\assets\\images\\wechat_pay.png.jpg", "hash": "d7e79fbda9bba9aa63a01c6279feb532"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart", "hash": "04e3db38362ad6238e0bd8394acf5b5e"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\widgets\\background_service_widget.dart", "hash": "f93915b7b4cc4ab339f8b5b248394dee"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart", "hash": "97fa80600d57e253a846a881c4aaae73"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart", "hash": "d765dc0eb274578ea0585d7066a563d5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\tiktoken.dart", "hash": "85d43dfe6fbf8c128fd0d7de122fe910"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\home\\home_screen.dart", "hash": "a479f8b4b4b364257031af38adc7ff63"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart", "hash": "9c7196e67b951143d548d72aaa0e75ec"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\node.dart", "hash": "eb8af6a9d85f134dd7d7c1ae2dd07e29"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_transformer\\stream_transformer_wrapper.dart", "hash": "04d38c19b0c3dba61b730122d76ec4d4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.2\\LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart", "hash": "d48d52bc573d346cad979541a2f68329"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart", "hash": "4326580ee93b3a584477cc185146eb2f"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\chat_message.dart", "hash": "d9eca08345428d880a7a45c4b3dda73f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\url_launcher_platform_interface.dart", "hash": "9190f2442b5cf3eee32ab93156e97fb1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\readable_stream.dart", "hash": "185e0cbf89137ddf671d9fca64c97b1c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\list_message_files_response.dart", "hash": "fa3853dee9fe8a93b31222750bb78cfe"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_multi_draw.dart", "hash": "073065873f7133a121a3e2995f6377db"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart", "hash": "787f46e13d1a634284a8403fe1fbed15"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing.dart", "hash": "1be64f7d80a3db5d33ac240131681562"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\editable.dart", "hash": "2281c6ad67f73aeed47ff6b11fbac51e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\text_style.dart", "hash": "bcbe75353032d77c9b99a24bb590393e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\newline.dart", "hash": "bdd138e5e3c721f9272da59c10d7c5fa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\LICENSE", "hash": "c458aafc65e8993663c76f96f54c51bc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_client-1.1.4\\lib\\src\\fetch_client.dart", "hash": "57028aa28a634a2cc52d7ccd59c77908"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\combine_latest.dart", "hash": "0c4028018783c732ca451e7fff693d3a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart", "hash": "7c956712fdb0e888676c78cc537b725f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button.dart", "hash": "2504b0f1dbb634dc6c4daee942c28df5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\value_stream.dart", "hash": "4ec7181b3b281703a8fddee43b540ee6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\colors.dart", "hash": "b62b9e4874beca4adb96d6ebfd8e8dfb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\optimize.dart", "hash": "a4acaadf6a817daad4c485f9c6741bca"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\duration_extensions.dart", "hash": "b7392495f5a412b000169df543ec9bce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\constant.dart", "hash": "84391293163d781c7715a32ce43b3c7f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_compressed_texture_etc.dart", "hash": "406426872f004adaa359fd9697e46d32"}, {"path": "D:\\element\\flutter\\packages\\flutter_web_plugins\\lib\\src\\navigation\\url_strategy.dart", "hash": "b4ba7ab9297afb87a23ecc982ce7430b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\helpers\\events\\providers.dart", "hash": "1603827b24b2ef8333181f7b49d83285"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\attribution_reporting_api.dart", "hash": "5001aaa956012cf3be30b4f1c7cf9efe"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_flutter-1.1.0\\LICENSE", "hash": "4329bcdd1ac50446158359963f9d3403"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\rx_flutter\\rx_getx_widget.dart", "hash": "464db0505a7eae94e2c04e2c7fcf2c7f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\helpers\\http.dart", "hash": "85eb2b5d0e8262c6ff2a3f28b63538d5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart", "hash": "30e8ecf1451da4c1923eee8e5ba4a652"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_image_request.dart", "hash": "cc4eae6374c12855b168bcf9b5ade21a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\generic_sensor.dart", "hash": "589d6d019d54515cce02c54dc2532c8a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\agents\\agent.dart", "hash": "7f1e8427a97aa19349c2c89fbb6def98"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\lib\\src\\magic_number.dart", "hash": "d9d40cd4fd7e692ca4246d952d48cca8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner_core-7.2.10\\LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\core\\context.dart", "hash": "a07f8e10a45176e8210b1bbac38f3e1a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iregexp-0.1.2\\lib\\src\\iregexp.dart", "hash": "fc7dcd17804ba2d94dd5120576fe093a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\loaders\\web.dart", "hash": "a6175b76d8dd3c84fbcb9f0a34001ffc"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart", "hash": "9f5e1e102c551e6d1bdd48d2f865db6f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\app.dart", "hash": "d03c5ad5b96ac93a549a524e512758e6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\response\\response_options.dart", "hash": "5a78f4a339699c7387e6e062c4cec60b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\runnable\\map.dart", "hash": "2c88e074db319730eb5c34dac04ae6aa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\date_format_internal.dart", "hash": "46f06f2d32f61a3ebc7393f1ae97df27"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\list_runs_response.dart", "hash": "421374483a4f15388c77a07765b4a523"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\resolve.dart", "hash": "cbb8e1af9f1f0decfb6fc25a0725c51f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\chat_message_history\\base.dart", "hash": "02a49dbf4a2d931f89b37c89cbd96971"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request\\request_destination.dart", "hash": "5f260259863f6480fa24b65b02025c03"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\object\\hive_list.dart", "hash": "be45023218a3803531ceb7521533bf9a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\arena.dart", "hash": "527f66bca3f4ace3771d5ffce977c225"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\registry\\type_registry_impl.dart", "hash": "74bcfa36a4954c05f1b8a9d5ed663c8d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\prompts\\template.dart", "hash": "28dccde148e1a51bc3fed567a37bcc88"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audio_session-0.1.25\\lib\\src\\darwin.dart", "hash": "30f864a2edad9c20e183a80c3b4eef42"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart", "hash": "254681ce32061015aea72e79d4a7b8ef"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\not.dart", "hash": "be4332e6d8c10f4a290e2a412399e1cf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\soft_line_break_syntax.dart", "hash": "cc5238a3fc99e9b9e90aec0dcb244ec2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart", "hash": "29426d64d0c201a4d7e7492434b13463"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\summarization\\summarize.dart", "hash": "694e03ba40a271781dbfa7603daae2b6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\where.dart", "hash": "bab2294ec70ff137aca684dd19203943"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\assets\\images\\wechat_pay.png.jpg", "hash": "d7e79fbda9bba9aa63a01c6279feb532"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\retrievers\\retrievers.dart", "hash": "fe64fb4da70885294ae876238ac71f8d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\fine_tuning_job_status.dart", "hash": "fddcc16b7c1ae6b60f4675988d72981c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\adapters\\ignored_type_adapter.dart", "hash": "b2ffb1a4d0254b77d2b63bfa6920223e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\enums.dart", "hash": "1d8b662e80f9cba7491aa553207d4ac2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\oes_draw_buffers_indexed.dart", "hash": "16101e10b183695e9eab803790cc4f19"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\web.config", "hash": "9dfa126e952b6dd00c19003ad2e39854"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\texture.dart", "hash": "e924e0f63c6bf206211fb6a6f319af53"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webcodecs_avc_codec_registration.dart", "hash": "5ddb1b86eeab0b5ae860487e9a07907d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\utilities.dart", "hash": "db8fd891fdcab94313f26c82f3ff2476"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\mathml_core.dart", "hash": "e3f8daeff0664c49cd50ac275a604523"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request_options\\request_options.dart", "hash": "96015ab2dc6c6a62c9af6b37e6aafb66"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_embedding_request.dart", "hash": "915d43ecbb4bebff6d98f0ff50c702f8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_flutter-1.1.0\\lib\\src\\box_extensions.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\email_autolink_syntax.dart", "hash": "f638de8d2c7ac422642d631198fd905e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_message_request.dart", "hash": "4944c112946c65c87804cbb88f7ee621"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\iterator\\iterator_symbol_method.dart", "hash": "93fd8e15ac0f5b0a551c8ad41d5d33d7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart", "hash": "99b984d75432877c137383cb96a09240"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box\\box_base_impl.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\just_audio_web-0.4.15\\lib\\just_audio_web.dart", "hash": "9345d617fa5d239232c1825363b88876"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart", "hash": "314c8a74e984655102d473063387e50e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\app.dart", "hash": "45ab06af8bb41c6fe8d5ab4d0faad94f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart", "hash": "1d7ce7e8ac466e0412e2976aac25fbf3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\LICENSE", "hash": "624ea47f75787d0b4ecfdfe22add0a0c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\embedding.dart", "hash": "5ead8e528067626c86daa2aa5c330835"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart", "hash": "e101d4f99c70b9f1ab98f5969e2c2111"}, {"path": "build\\web\\assets\\shaders/ink_sparkle.frag", "hash": "ecc85a2e95f5e9f53123dcaf8cb9b6ce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\adapters\\big_int_adapter.dart", "hash": "f962a26b7944264455f9d479c898f535"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\style_manager_screen.dart", "hash": "3aaa95902d4ea3ce5bb0827341692e5d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audio_session-0.1.25\\lib\\audio_session.dart", "hash": "8aaf3b69fb45264a9ff7160596f4281d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\cookie_store.dart", "hash": "7309588fb9792c7b1e40d19ddb5f8fe0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webcryptoapi.dart", "hash": "77fda802f54858a88d7535227bb1ebc4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "build\\web\\assets\\AssetManifest.bin.json", "hash": "2f1fb0bd010a06931e7166d88a885736"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_macos-0.9.4+2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_core\\src\\log.dart", "hash": "cca20d2e059754fabfd9573f21f78d03"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\chapter_edit\\chapter_edit_screen.dart", "hash": "9af63fb41e30a1194bd5dc92cd0eba64"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\async.dart", "hash": "13c2765ada00f970312dd9680a866556"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\behavior_subject.dart", "hash": "86d361932e590380696b3189090d1034"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\multipart\\multipart_file.dart", "hash": "72a5a8118beb29efdfced572776e5f72"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\util\\indexable_skip_list.dart", "hash": "eda351b39b4854648a4d265ed1605fcc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\hive.dart", "hash": "f038e71fe3279bb9c67e5ef28b3e8afe"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\lib\\src\\types\\base.dart", "hash": "86039b13313ad468f867bb5522411241"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart", "hash": "f06bc0318a4a0ecb95b7384aee5b21ca"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\chat_models\\base.dart", "hash": "e65b283adac587487d3ff6a2b3f3813d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\release_sink.dart", "hash": "e2f7d6fbeb362176a24cb422a6dd8193"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker.dart", "hash": "e7d65688f59374e96d1cb96300649849"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha512_slowsinks.dart", "hash": "76b9af381da547215b8af856567ae186"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\list_assistant_files_response.dart", "hash": "659a3f441e91c0906f44e7a8e12eb9ce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\src\\bad_route.dart", "hash": "81c1607c7af87de0c74ea1921f233c6d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box\\lazy_box_impl.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\setext_header_syntax.dart", "hash": "681f4ab4e9322ba03caf814cd9835e16"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\clipboard.dart", "hash": "f63442b56372cdf284974de30fba136c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\future_group.dart", "hash": "fb71dd46672c822515f03f8f0dddbcb8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\material.dart", "hash": "e0fc58cbe26c8afbae96f489212595fa"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\pubspec.yaml", "hash": "96a311554ed4643b360ab691056fb105"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\dom_parsing.dart", "hash": "341172e2f74267b9345cb7cecfd16d2d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\oes_fbo_render_mipmap.dart", "hash": "1c661453d0be382d5fee4fc5863cb953"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ext_float_blend.dart", "hash": "1347d790ca01704ce589d0e001b9f24f"}, {"path": "build\\web\\canvaskit\\canvaskit.js", "hash": "86e461cf471c1640fd2b461ece4589df"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\.dart_tool\\flutter_build\\b7a4acd900463a69943b557d4d9a654f\\web_plugin_registrant.dart", "hash": "2d52c23d5ee7a2d482448a99ff2c718b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\referrer_policy.dart", "hash": "1239848c03a1587a30731bd89231ddb6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\json_path_match.dart", "hash": "b66c453feeab3515eb5af3b50fc356df"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_client-1.1.4\\lib\\src\\cancel_callback.dart", "hash": "a410cdcfa9b3884eec7e735a111fefd7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl1.dart", "hash": "1127949efc41840c01de5f126e84bcfd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart", "hash": "52d3612bdffadcf247e4570767678989"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart", "hash": "93ea53606b5bcb9f94323580b8a73a66"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\connect.dart", "hash": "0a9bb7a5f4b05c123f0b232e5a6b2db7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_transformer\\handler_transformer.dart", "hash": "81a6a107cbfd5dc1c55af9a93189bc5d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\http\\request\\http_request.dart", "hash": "eb7f4f7781a1c24c13fd7fa72a360ad3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\retry.dart", "hash": "66c3d8022ecd26ac3b2f30fe28e4c475"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_web-0.9.4+2\\lib\\src\\utils.dart", "hash": "569162a8c7146d766102402b026e2b36"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\nav2\\get_router_delegate.dart", "hash": "2a1776701613107770b66633615d7c7e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\just_audio_platform_interface-4.5.0\\lib\\method_channel_just_audio.dart", "hash": "9299b7019ded48cdc6b1cf5898ceb98a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\object\\hive_list_impl.dart", "hash": "6f02ecb5b09b8edd2a435707a8516cef"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\status\\http_status.dart", "hash": "f25262b32f35da6dac615ea4aa4b8bf4"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\adapters\\aliyun_qwen_adapter.dart", "hash": "8b75dcd6041a33849cc6a07b31c1bcbf"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart", "hash": "249b2817c1c912b9942e8acc7987adb0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\image_cache.dart", "hash": "714b1699c750c75358296e96a1c16573"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\undo_manager.dart", "hash": "cdab58168d88bf8cc4f48120b49ae69c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart", "hash": "007e3dfc7780af8def8a39f3be6e0ebb"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart", "hash": "05a65ef87faed70eb436b68ecd4a93f6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\message_codec.dart", "hash": "16cc4a3c97bbfb29764163f9783954cf"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_web.dart", "hash": "dea6cd20866ea4f980b1b89e6c362a8b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\constants.dart", "hash": "c6dd0c20e5521905acdd0e209727ec65"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\assistant_tools.dart", "hash": "67e23eeef592620b4c86d7dfcac80683"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\root\\parse_route.dart", "hash": "4fb423cedf15061462aee3b85d867f48"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\iterator\\iterator_method.dart", "hash": "67363d3967b754f8c8374dafb9d6aa58"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\response\\client_response.dart", "hash": "f27ead2aed342bd71eeaac0ad838aa51"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart", "hash": "93219dc70f767a24408583015695d38d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_parsing-1.1.0\\LICENSE", "hash": "96ed4c0b2ac486bba3db2c5d2a96afc4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_stream_response_delta.dart", "hash": "05581b74cfe6579869ca4c04d080290b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\build\\web\\assets\\FontManifest.json", "hash": "99914b932bd37a50b983c5e7c90ae93b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\ai_chat\\chat_settings_screen.dart", "hash": "5018c96f076b6aaf932618aafa06e501"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\can_stream_requests.dart", "hash": "99eba36b7ae2024184097b369725774e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart", "hash": "add0c413747369b7460aa14539b29791"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_multi_server-3.2.2\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\touch_events.dart", "hash": "99587cf948b50333494149c8effe0d3f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\licenses.dart", "hash": "43be915d6729f5b59a5dc76fd923b31b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\theme.dart", "hash": "fbc4e27193ffde95719ac936bc35071b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\array_slice.dart", "hash": "c74923bc2e3628fe179c46a244083c18"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\table_syntax.dart", "hash": "be75bc0542091c605701a2e841423b3b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_define_property\\js_accessor_descriptor_setter_method.dart", "hash": "8f8feb1e2acafcc76589d279c9541071"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_compressed_texture_etc1.dart", "hash": "7b2c75d16ca438685c32ac70d9af609f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\interval.dart", "hash": "b2a2c73b0b7b528180181e9e4e3b4e92"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\petitparser.dart", "hash": "6cb32004f228090f1200484076254c7a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\restoration.dart", "hash": "7baaa1303e02f774edf72ccf25381bfa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.12.0\\LICENSE", "hash": "aca2926dd73b3e20037d949c2c374da2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_token_top_logprob.dart", "hash": "3ba69c91d1a8c83a2cd767b7dcba048d"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\build\\web\\icons\\Icon-192.png", "hash": "99914b932bd37a50b983c5e7c90ae93b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webcodecs.dart", "hash": "7f7e5fa40c1f82049989d2691da38e0e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\token.dart", "hash": "6c8afaf3db5be20a458530a92ff971d5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\io-1.0.5\\LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\stream_sink.dart", "hash": "ef83fcd13366d1d61c5dbb5c6aae5ead"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\string_extensions.dart", "hash": "5bc7d4e23fbbbcf2b32f34a951b5ae4e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\maybe_just_nothing-0.5.3\\lib\\src\\nothing.dart", "hash": "e897d10fb57c5a45b469c6eaf15365d7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\magnifier.dart", "hash": "de9074b4c33d599d09ff4a2b953bc3c8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_fonts.dart", "hash": "a26d8d16b5f7d1052db1c0c8cbb1f8d8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\view.dart", "hash": "2d6edcb951c82d23cc3590b236a2e0f5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\just_audio_platform_interface-4.5.0\\LICENSE", "hash": "fba1d88f08129f8046dd091b8abb5ded"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_view_transitions_2.dart", "hash": "fa4a3e6a968f48ffbb520a01d20a34d4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\util\\extensions.dart", "hash": "a9e0df3a9079b0f6b5041cf4d901f932"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\switch.dart", "hash": "16421b0a1dc5136a0b6a71b8d63b3a27"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\skip.dart", "hash": "be231020db4ff03ccedf0cab8d50d12d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\streams.dart", "hash": "25a929555febc01ae405a334b5ab9ce1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_client-1.1.4\\lib\\src\\fetch_response.dart", "hash": "0546efb0707b51a1a39b69aed201b4ac"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\object.dart", "hash": "edaa45ed2b936339d8dbfb70b253edd4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\debug.dart", "hash": "49bd72b2f6d097b43a24ecd799f3d75d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart", "hash": "0827246acbe8e59d534a6625ad860e7b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\ink_splash.dart", "hash": "e91a73519c927b9535a83b357801e052"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart", "hash": "72ede1aea65e26bd0e0b44da56f41473"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart", "hash": "462f8fa010cd5b4b90e61714d27a9954"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\trimming.dart", "hash": "1db476563b55e241003667ca3669c0b8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\wildcard.dart", "hash": "843a962e2c80597491e01a7b22943355"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\delete_assistant_response.dart", "hash": "8153ccf9fd146fa1575817ee750e1ca1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\filter_selector.dart", "hash": "09e09fc47adaa5a93545d75f4e488847"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\video_rvfc.dart", "hash": "9bd5317dcb318d2a314ef885a62bb243"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_storage-2.1.1\\lib\\src\\read_write_value.dart", "hash": "9844b9ce3feb87df822a057dd946b866"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart", "hash": "d7c562d566e2e21539ea8288f21e7876"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart", "hash": "1245b0b05f60c57164018c5c918aa7d3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\fun\\fun_call.dart", "hash": "3eb85e46e3075bd7204d54006ba7a45b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\helpers\\events\\streams.dart", "hash": "5d85e68dab1c562040338e8166c9e6b5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_gen-1.2.6\\LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\prompts\\base_chat_prompt.dart", "hash": "a0636685f593d91cd5f7ccefb251efac"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\node_match.dart", "hash": "f2d36f245b1242a2a8d3e97064d6c478"}, {"path": "D:\\element\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\web.dart", "hash": "65a32469dd972a4319f5a3ed81613321"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart", "hash": "7c562abf5bd2819e159d8b62b633bd68"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\menu_theme.dart", "hash": "0908983136fa6ff9c6aa302e79ee7bb6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\storage\\storage.dart", "hash": "802595758eb2a08ea3d04ad0fdb7432f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\root\\internacionalization.dart", "hash": "33d618734f1f85bf9a714a24bca086ed"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart", "hash": "525ce10fb97ccc6f3407f8e962043ca5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart", "hash": "d70b537edfcda82387a991a99e6700d1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart", "hash": "da8c02e232d7b61d4c38ed6f84826f17"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\entries_api.dart", "hash": "800ce0cca8ce3af4fd3a21897cfc28f6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\binary\\frame.dart", "hash": "e28d4397780eecba27eaced013118898"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\screen_orientation.dart", "hash": "4fdc43d22013e6a2f9c8e301e80c7096"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\slider.dart", "hash": "0bdf9ad7c5dfd2f151dcd0b72f24d941"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\token.dart", "hash": "a27310d4435c84885993bedb05adabfe"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\combine_documents\\base.dart", "hash": "89e9d77bce4d234a2cd91df5072448a1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart", "hash": "92b14b79401bcec16f2db5541e7d9da0"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\controllers\\knowledge_base_controller.dart", "hash": "f6ed942af8e9250e9097bdec15e0b7f6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\debug.dart", "hash": "624431304ab3076b73b09e0b33e35e4f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart", "hash": "bf2ed9d304c7f4d1e982b118bbe93bf2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_serializable.dart", "hash": "c6f78ebc1239a030ffc141df9b33aed1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\service_extensions.dart", "hash": "217b7c2fd7b1eccde5897e1f17fdccf9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart", "hash": "8120214a606fbc8c98cfff2b27eca1cd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart", "hash": "********************************"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\binding.dart", "hash": "00b7db0e816918eb1c2a0caba6162bc3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\number_format_parser.dart", "hash": "61a0deef2a4f0ebaed506bb2a22c5185"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\common\\utils.dart", "hash": "6c777cad0f930bc55be53bbde40a8e0e"}, {"path": "build\\web\\canvaskit\\chromium\\canvaskit.js.symbols", "hash": "5a23598a2a8efd18ec3b60de5d28af8f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\abort_signal.dart", "hash": "9e92d7d94cf53fb2ac4617e593f0865e"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\writing_style_package.dart", "hash": "fb28f4cf1ca82c73b7a41b1b637abdd9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_until.dart", "hash": "85fcef4d360ca759563bbfbe7c8d5e8d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\core_bpe.dart", "hash": "f79ef1e42b66c55a4b6ed446c60d88b9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart", "hash": "153746880bb69212744baf97aa5f820a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\delete_model_response.dart", "hash": "a3a6bfc9674030fa116a4b936425068f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-4.2.0\\lib\\package_info_plus.dart", "hash": "f58c4450f77f117c177002f4e4ad4d17"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\server_timing.dart", "hash": "fcbb7d84b5581cb366a304d13a9d957b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\iterator\\iterator_result.dart", "hash": "45497eb3b3b3985e7f2c36ec819364de"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\dialog\\dialog_route.dart", "hash": "6baa5ed3784bbcb8e49868803239f6e7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\inline_span.dart", "hash": "a0069e1a865d2951028c9b835bf14f15"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\text_syntax.dart", "hash": "8acb16c991a25426b4fbc2c6bbfdf213"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\output_parsers\\functions.dart", "hash": "e4660382a84f1a1aadde77920685fa24"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\lib\\src\\x_file.dart", "hash": "d06c42e6c83be207b86412e11889266a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart", "hash": "166a96f7e7817372a8e04dc681869e61"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart", "hash": "430a92b7fc96d89d9750f26f826484bc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\parser.dart", "hash": "7aac958977c79edf01e6ad44a726b52b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart", "hash": "2d546104cf408c2277ebf73c3a60d051"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\events.dart", "hash": "7f694f69cb60ba144f59ff752805476b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build-2.4.1\\LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\error.dart", "hash": "60f88878959c8f38a1f8cf481bc3d76a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\double_extensions.dart", "hash": "2c2dfe1e2afc0ebdbb4cca417e6af35d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart", "hash": "462a04a96a1e876c39d3a790b9d9cdb5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\buffer.dart", "hash": "968fe5f78bb200659f85035bd2d8866b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart", "hash": "97fc1400dd55cb4fceecb33260f0f978"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\background_sync.dart", "hash": "8274d7a1aa4341e38d8c81b9b16ba5e0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webauthn.dart", "hash": "016492ab3715179209a3c8648fb4665e"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\flutter_canvaskit_config.js", "hash": "5e958eed053e64108c20c8e604da8ad8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\moderation.dart", "hash": "458c878ceeb8c9e8a283dfb88bd93770"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart", "hash": "24314571db406eb9ca7f0823eedbe105"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\types\\file_dialog_options.dart", "hash": "c7a750b73798e6fbab221eff051e22c3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\src\\_internal\\object_member.dart", "hash": "40324c590e47651f47f65f2aff4aebe1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\route.dart", "hash": "d342beb57d6c513362bc3c7f9b4993a8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\annotations.dart", "hash": "b98145bd156a782be220cb3f652ba0a4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\single_subscription_transformer.dart", "hash": "789cc727406d0343a1dddb5018570adf"}, {"path": "build\\web\\index.html", "hash": "8bdd6209f4d065ceffb954e1b2bfeda2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\materialize.dart", "hash": "7787d9ce2aed834062cd38b022824d31"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iregexp-0.1.2\\LICENSE", "hash": "83f9d79f7e571727b46c758b90bf7eeb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart", "hash": "3b88bbef523e78633182df3f70dbe5e4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\input_decorator.dart", "hash": "42718a834237103fa5daada18e905d8d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\nav2\\get_information_parser.dart", "hash": "3105039a1093bb8e0b2d5d9df62dd2fd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\permission_status.dart", "hash": "e644eae6cf851b3c46f83af266811a6e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\chat_message.g.dart", "hash": "586fe58d6e4bc78ecc933decc5db5c22"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart", "hash": "3da0a4c6b5e4be03fa0e8e2871e98d01"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box\\default_key_comparator.dart", "hash": "e9e745187c355ae5f27e291fef7cc27e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\helpers\\extensions.dart", "hash": "54974b54397f63e417b9ffa24e4d6922"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image-4.5.4\\LICENSE", "hash": "c17706815151969aa7de6328178cc8bd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\input_border.dart", "hash": "dab909dedbbf46bb14d7a26091ac10b7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\user_timing.dart", "hash": "2c6f052293c9b2a6f27563e70ec2900c"}, {"path": "build\\web\\assets\\fonts/MaterialIcons-Regular.otf", "hash": "7351d4194c70192adeeac983cf3b81d4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_helper-1.3.3\\LICENSE", "hash": "3b83ef96387f14655fc854ddc3c6bd57"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\node.dart", "hash": "997047ccb07836b306d9c44c133433fa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\negation.dart", "hash": "551355823e8ae6ef3cdbe2c397530395"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart", "hash": "d577bed3a701a1634f7130060e4e950f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart", "hash": "f3a43f7f1e3cdd35326341d9fe51fdee"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_types\\rx_iterables\\rx_map.dart", "hash": "a2f4f66e90cc8fcb658ffb0241d258ca"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar.dart", "hash": "c49095d5390fd337df5431a0228cbd3e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\tiktoken.dart", "hash": "c884c8c545560a4dbaf76501d8dfd71b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\math_expressions-2.7.0\\LICENSE", "hash": "ca5da27b00a5ff67db2e18e168f076b7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\prompts\\models\\models.dart", "hash": "4520f11750d4cd155c7552fe311f91a8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\transformers.dart", "hash": "5d2971806de340d9e970e21af445505b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\help_screen.dart", "hash": "ddbcb083ae3366c7494c0b315ce38a7a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\barcode-2.2.9\\LICENSE", "hash": "5335066555b14d832335aa4660d6c376"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\ranks\\r50k_base.tiktoken.dart", "hash": "61a6c3213f871d0952b53102ac90daa2"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\novel_continue_service.dart", "hash": "229dbdc2a7d7f5f2526b6b1eb2609999"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\menu_style.dart", "hash": "ba859da84d2120e70ef3404c552ab425"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\list_paginated_fine_tuning_jobs_response.dart", "hash": "9c55f07595f4fb4912338256e8374a92"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\print.dart", "hash": "d073924ebcc169bd1a47c3f695925478"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\routes\\default_transitions.dart", "hash": "3312ed438b25f93609ca343db43e68e1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\sink_base.dart", "hash": "8fec1bb0c768b230066dba96aac40ff5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix4.dart", "hash": "6250cc05770b9eca7a8010eaed7e5b94"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart", "hash": "3f591c8127c07a81900c2b23dc82a909"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\instance_manager.dart", "hash": "bf13d480d7f19204700072d2f4e61111"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart", "hash": "a400dfa676904fa8a84c48146ff8e554"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\star_border.dart", "hash": "ed437fef28462d73a74c339fc59a6cda"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\input_chip.dart", "hash": "92c59c394e56bfb4a3d764c714a45c1c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart", "hash": "bacca4c03cd5121bb38b2cfcbde14197"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart", "hash": "ce79f0f4b543a5fc0d4137792329e870"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown.dart", "hash": "fd9660a74c616c1ec25407f552a6d001"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart", "hash": "bbb8de6dfd910b8abb564969c260aca0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\checked_helpers.dart", "hash": "c668a1bfe65f14c115a3294ac6502dca"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\dematerialize.dart", "hash": "6f6ced37453e06f063a482bcb9509370"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\update_service.dart", "hash": "66737e1ba33a32b990479cf98dbef762"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\subject.dart", "hash": "4b93fc559e6626b4d42e924b10c58678"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\state_manager.dart", "hash": "e8536532f54922f5e9db210f4ef1bc6e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\common\\byte_array.dart", "hash": "9f01f922bc42653a16cddedf44014a18"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\http\\interface\\request_base.dart", "hash": "c987c8fcf9461b27c244e3b2fdf5fb71"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\embeddings\\cache.dart", "hash": "fd11148c38ead771fa8de190576d4160"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\normalized\\name_selector.dart", "hash": "f1a9575ac6f3b6fd9aedbcd55f28dc38"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart", "hash": "095664e8bd32f2d7ba1abab1804b52be"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\retrievers\\base.dart", "hash": "2b59d0a5ea589290d8e3e89c675f788c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\comparison_expression.dart", "hash": "ed7957b2d933fc5a5fabe2cb816e9748"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_last.dart", "hash": "08f42ef74f129fde820b3414026b8d34"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\agents\\tools\\models\\models.dart", "hash": "1a4b35b937c50301cabacfb73230910d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector.dart", "hash": "7ba48caa7a6a4eac8330274dae899e48"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\timer.dart", "hash": "24a365985ef5e526e029d73522f4f2fd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_define_property\\js_data_descriptor.dart", "hash": "6e8973224383a3091d09aef7b7349c83"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\dialog.dart", "hash": "a6592eb83a3e0ca592dc7eea841035cd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\adapters\\date_time_adapter.dart", "hash": "cb28076c9c2d74bd04b62483c2e63193"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\internal\\undefined.dart", "hash": "bb00c98e50d3c71d4ab7ac7c46122f3f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart", "hash": "27bd43533f409e87e38949f0aba72a1b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\root\\root_controller.dart", "hash": "c54823462895e34a8b8ebeda4ba5817c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\lib\\src\\int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\fun_sdk.dart", "hash": "6b63e462407e1886d71975e3b59527d6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\list.dart", "hash": "ee730199a496cacbfd82312849e80523"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart", "hash": "7a67893da4c81183e350cc0ecc25667b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_common\\get_reset.dart", "hash": "4f752b039076bd15ddde3db15df4398d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\subscription_stream.dart", "hash": "b637f236939a0af5ddf1bae124669288"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\utils\\exception.dart", "hash": "2fe47cdcdaa3365999d0938d73407737"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\binary\\binary_reader.dart", "hash": "7f909b315b723d7060fa20f099d03ba7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\scheduling_apis.dart", "hash": "b2b6fe6c3aa455fbcc2731bade5eb5e4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-11.4.0\\LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\tab_controller.dart", "hash": "90d9ef8c98ba928aace1e5b7099c0844"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart", "hash": "9b9ab2c0595d95cef9e27ae03e36991d"}, {"path": "build\\web\\icons\\Icon-192.png", "hash": "ac9a721a12bbc803b44f645561ecb1e1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timing-1.0.2\\LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request\\request.dart", "hash": "239a3fb200aea940951dcb82e3d65f57"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart", "hash": "c909abaa7c0b6b52aa22512668ededb0"}, {"path": "build\\web\\icons\\Icon-maskable-512.png", "hash": "301a7604d45b3e739efc881eb04896ea"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart", "hash": "64217fbeea7b61154f3de66a1c34ad93"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\browser_client.dart", "hash": "cc781b4a7a4b4498f839f39f72c73d06"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector2.dart", "hash": "6b519d909b25ca9d144af7972d689c6f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart", "hash": "1d3152d32f76b361fabfc04ad1eb74b4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\text_formatter.dart", "hash": "6ebce6d2b9c6488571c530cdd3c0a723"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\interceptors\\get_modifiers.dart", "hash": "e92cc9937cf76026bf60428a151ef9c5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\switch.dart", "hash": "0878e74033cd8622ea68cdaba6145739"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart", "hash": "35d5b2c786045d1aa7428d0b48b2b3b8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\assertions.dart", "hash": "5c030253fc30fc8c455a33b560bc8ded"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\runnable\\sequence.dart", "hash": "662efdb460f255590ee3f7b6afbd753b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\simple\\list_notifier.dart", "hash": "f9a1fa6341d6c1fa8f02d90dcb17063c"}, {"path": "build\\web\\nginx.conf.example", "hash": "3eabafb024a685f2d3b6f8139b54a757"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\platform\\platform_web.dart", "hash": "6c1a9e5c669f9e15c176df23fab29ce8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\gyroscope.dart", "hash": "9cbb8f979e1c128e4df7a7fb9e8bd7a0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\visibility.dart", "hash": "2535b76e646e95222aa41ab42951de84"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart", "hash": "c613d7434cec89361a3b831cc91f86cc"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\widgets.dart", "hash": "7e8b0fb2c40d382523feef7e49d360d8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\action_buttons.dart", "hash": "e71bfc49f52579c2e458315e9067527e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart", "hash": "4297b644d258ee7771676fea206a5118"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\clipboard_apis.dart", "hash": "30e5d39c45acc953b5bdcce6baed9def"}, {"path": "build\\web\\assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "33b7d9392238c04c131b6ce224e13711"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\tokenizer.dart", "hash": "a8e51be045a7977648c023a4531317f2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\autofill.dart", "hash": "31a2a7c5aca2c45f6d4ffa7399520690"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\oes_vertex_array_object.dart", "hash": "aecfb0965bc148911ec391faf91e7417"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\tools\\tools_screen.dart", "hash": "0be8d66b307fefe9fd689055050dff3a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\src\\json_pointer.dart", "hash": "d6e3a08d806c6545e9457cfeb53d6a3c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\buffer_window.dart", "hash": "c3ce489b8c7513d88c088996d8f979fa"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart", "hash": "e657634a020bb8214e2507b5d0105d6b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\completion_usage.dart", "hash": "60845f3afc2b1b73f7e504e260da9683"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\trusted_types.dart", "hash": "492de3051f108aac26fbbf7f15f2dc62"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\subjects.dart", "hash": "1f923e6c3ab08753130b61ba27256959"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_draw_buffers.dart", "hash": "eb114ec5ef68168fddc81eca33e321f4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart", "hash": "d65e92ce2b27908a96f7a6efbb9cdcb9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart", "hash": "c1329f33c1eee5551ec4b5b013167e71"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_tool.dart", "hash": "9d587ff7f290614346599659f86027c5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\on_error_resume.dart", "hash": "f79083ce7919dc45b4d2c313bd37af7f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\backend\\js\\native\\storage_backend_js.dart", "hash": "241a211d83fdbe9c145cd48b0be3d948"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart", "hash": "57699e53ee54d843153d1ef4dd086d64"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\readable_stream\\readable_stream_source_cancel_method.dart", "hash": "54e3501800cb605e7b4ad03aa449ad0e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audio_session-0.1.25\\lib\\src\\util.dart", "hash": "9eb1f8ab2bfdd218f9e8c25acaea4233"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\combine_documents\\map_reduce.dart", "hash": "3a69be4316b71ddfde09885097857638"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_types\\rx_core\\rx_string.dart", "hash": "c597f343fa88b0de1319e2879ce3edfb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\fun\\fun_validator.dart", "hash": "b15da0663283867adbf7eff3e3ff703b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart", "hash": "fc5da3ebf9e3e6d691265758e9d339be"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\loaders\\json.dart", "hash": "f174c82e43b4b5c6238303be6f0a6eec"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\unordered_list_with_checkbox_syntax.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\http\\src\\http\\html\\http_request_html.dart", "hash": "109089e47acbcdb6e02413ed89b62431"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_lose_context.dart", "hash": "ee954c303b5a0b6a262df5dcce771a1d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_define_property\\define_property.dart", "hash": "13bb746e4fc876805bde653435504f03"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\switch_if_empty.dart", "hash": "f1236a5e582b3794b3fb2302d7297451"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "build\\web\\canvaskit\\skwasm_st.wasm", "hash": "3e4cc56561a4cac78b18c30bc5e73055"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\ai_service.dart", "hash": "f48fb13225d846645ee2af9cb0cef473"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\checked_yaml-2.0.3\\LICENSE", "hash": "39d3054e9c33d4275e9fa1112488b50b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_7.dart", "hash": "c3e5aaaf36524bf9927e80f60f3b0bdf"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart", "hash": "9f600f4245c898a4067344ec13a38169"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart", "hash": "dccc4c7ff5d008afb92d48caaec777c8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\summarization\\summarization.dart", "hash": "be70f47fbeeed986f691a92899c1866a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\flat_map.dart", "hash": "115640739fe47a728c4b1c3a4b4c3506"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\definition\\internal\\reference.dart", "hash": "f25bbc73708cc35ac55836cbea772849"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\transformers\\text_splitters\\utils.dart", "hash": "a79cecf51c694b229a42f85bdbc3de5d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_define_property\\js_accessor_descriptor_getter_method.dart", "hash": "1d7e8d2100152bf088b9d4cbbad44536"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\gamepad.dart", "hash": "3b6116b8e01fe069a2233912fafbca0c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\text_theme.dart", "hash": "334531c37758b9a544d5560eefafe5c0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection.dart", "hash": "89a52c840b1bed53ea3c5479d6190762"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\widgets\\common\\animated_card.dart", "hash": "927952ad8f345ba825b8d38de09dd39d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart", "hash": "7e1916e19ed191fb74d54f4e67662098"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart", "hash": "0469ca72d371bc48cf7f0901c0cd1917"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\data_table.dart", "hash": "7862b5525e8ab7824b90de6fc30411d3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart", "hash": "c8fcb896b99e47b82faded7eb00f3a68"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart", "hash": "ceaa30badfe954312001275aa51ba09e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_client-1.1.4\\lib\\src\\request_canceled_exception.dart", "hash": "6c5bc263fd1c667bff0c471c246e9f50"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\button_style.dart", "hash": "399b64e5839e1a71d027672c0e0facc6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\limited.dart", "hash": "bfc3692929b6ffa40605428f3cc70e86"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\lib\\src\\share_plus_linux.dart", "hash": "215ea407318b75782d0a36692e036ca8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\llms\\models\\models.dart", "hash": "353571469ab26427c0ea30c512cbd2ad"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\binary\\binary_reader_impl.dart", "hash": "7a1a5e4d4978935357c5815297b253f4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\crypto\\aes_tables.dart", "hash": "e086df7291d9d546cf582d0a519f9848"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\paragraph_syntax.dart", "hash": "d6a6c71982af08a095882f291e4ac12d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\code_syntax.dart", "hash": "b6ecfcb99dcf7df32e9b7c185df1dc8b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-14.3.1\\LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\date_builder.dart", "hash": "2c721a938baa2ccf947cc49f3e6cd4e8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\lib\\src\\permissions.dart", "hash": "********************************"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\theme\\animation_config.dart", "hash": "ee4e74f8b857c904339e1901c7ed072f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_types\\rx_types.dart", "hash": "d101770e634e82dbbf46d10582ca4c65"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_core\\src\\typedefs.dart", "hash": "62625396e083e7e3934e524b94bd43fd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_tool_call_object.dart", "hash": "5998b5f604f7fb4487cfb4dab6abb2f4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\src\\formatters\\int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart", "hash": "905e412b364d027891e90040b270ebd1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\url_launcher_string.dart", "hash": "27e6c510107a34001ef90f889281633e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\src\\_internal\\reference.dart", "hash": "1c05b67af1f196c45fc92789128c31c6"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\ai_chat\\chat_sessions_screen.dart", "hash": "7964ce88dced1a7b4c0dd07c946c9a42"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart", "hash": "ca40852851f196d4416da400314a610a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\html_input_stream.dart", "hash": "ed02ce14880085c75d4dbc4b3145371d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\alignment.dart", "hash": "9b72ebf21b5cff1533e08602ef606a2f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\core\\exception.dart", "hash": "5934d7c0ee1ff94ec21aad560e9ed8ba"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\character_card.dart", "hash": "1a449686b86daceb1382bb8c18b78600"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\serialization.dart", "hash": "6557b0521d669043fe957bb915c97e38"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\documents.dart", "hash": "083d282202e01fd867d839ed39434158"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\_bitfield_web.dart", "hash": "9ddf41709c3f2ddfd663ad2e7c7ac3b2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\embeddings\\embeddings.dart", "hash": "a2461823bfadb9884c4bee605cab4535"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\just_audio_platform_interface-4.5.0\\lib\\just_audio_platform_interface.dart", "hash": "6a49b86842ace7ce1cc5a6874b409ced"}, {"path": "build\\web\\canvaskit\\skwasm_st.js.symbols", "hash": "c7e7aac7cd8b612defd62b43e3050bdd"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\utils\\network_client.dart", "hash": "3c4e7bb50eade0ad93c6a75d7ead9082"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\modify_message_request.dart", "hash": "b29505f6d19b29643f68a1075d9556e0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart", "hash": "38d5b82b40702a77199cd5c8be506036"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_body.dart", "hash": "3596cfc55c4bf6413b1781f6d498199d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\lib\\src\\keys_extension.dart", "hash": "eccf57aff3bed39266c0358b9b81ae9f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_client-1.1.4\\LICENSE", "hash": "5b9e0abd5b94088a3b85c38473db456a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\skip_until.dart", "hash": "22ce2f0be207fd716e4ae18e312f5cf0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\zip.dart", "hash": "636229be247a1ecd50a669eb2dc73206"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\routes\\get_route.dart", "hash": "97707058b5f65df4b815c3925e24493f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\divider.dart", "hash": "4ec01b337044bd094023a089bbe991d6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_message_function_call.dart", "hash": "88532bb95fe89dd1aabcad184faebc16"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\icons\\Icon-192.png", "hash": "ac9a721a12bbc803b44f645561ecb1e1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport.dart", "hash": "f1356e0225ee578884225acbdbb2c163"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\LICENSE", "hash": "e5c509c88ba5deea93f23433c98752c2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-3.0.2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\with_latest_from.dart", "hash": "eab456316eb52f6f668d1dd2800c1085"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gbk_codec-0.4.0\\lib\\gbk_codec.dart", "hash": "bbb07ec4642907634d9acc6082885734"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_stream_response_choice.dart", "hash": "745836a200dff5627a37a8c4d3d2ced8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_serializable.g.dart", "hash": "7ff35a1db7f2b80a156d464b075a09f3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\paint_timing.dart", "hash": "4c622e5476419d4783b3367af90e04a0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar.dart", "hash": "89fc9f415c4f18a79bebbe88c7473c02"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\isolates.dart", "hash": "8631e44e103ca1be44ae10252f3dbacf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\image_syntax.dart", "hash": "c11f5b5d7da77926328af66c27181c87"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\interface\\element.dart", "hash": "9d98a17fc3dcf9698ea3f5724202cd7f"}, {"path": "build\\web\\canvaskit\\skwasm_st.js", "hash": "d1326ceef381ad382ab492ba5d96f04d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_types\\rx_core\\rx_interface.dart", "hash": "14c135eda965d8610175b432df423b71"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\LICENSE", "hash": "4387180f1653e5266df9e9f028629d82"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\end_with.dart", "hash": "f95bd67282cf610843bb37b5784f3eae"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\freezed_annotation-2.4.4\\LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\union_selector.dart", "hash": "b040e0efb723070b6c799e9325255719"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\output_parsers\\string.dart", "hash": "6eedea3dc4c12b34e2e35cc63f52e3e9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\async.dart", "hash": "0a756bb43feaebfaf3245f96d8275789"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-4.2.0\\lib\\src\\package_info_plus_web.dart", "hash": "e41b64dfa21e7854ad8872354418dcb6"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\text_input.dart", "hash": "92bb595b5835e43ce086897fa4718a89"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\core\\result.dart", "hash": "fbca1545a7230f0ea39d7884a1722475"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\agents\\tools\\models\\models.dart", "hash": "e8aee97fde25a0a1741a3981e8b974a5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_instance\\src\\lifecycle.dart", "hash": "d9ec525ed8ac2b415731ef98a9458184"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\get_state_manager.dart", "hash": "c0c2dee84177efd3e87e479186e11852"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart", "hash": "b3121777aac7682030fb721537e2d6dd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\vibration.dart", "hash": "5e1dd34b3c889f65885f5175968648b7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart", "hash": "8e02b91714073f6161e7be7f9d8160c2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart", "hash": "7e0d9b501a852b39490804f01eb95ab5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\src\\_internal\\empty_json_pointer.dart", "hash": "a70deb17b6e709a433b2c2bb26154632"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\vector_stores\\vector_stores.dart", "hash": "160dec9fe6395553f50f18ecf3f5a557"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\battery_status.dart", "hash": "d8ec7796f593e2c27622cf1982f24c33"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\css_printer.dart", "hash": "9a6fff298db26d4e059ebb664863ab18"}, {"path": "build\\web\\assets\\assets/version.json", "hash": "457bbc1c02ec83827cf28cac703db301"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\novel_vectorization_service.dart", "hash": "4367c2955cd59788448967b62b9afbdd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\optional.dart", "hash": "7d49b944ccc5ee228590126488731a95"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\runnable\\runnable.dart", "hash": "6e6febb6005f1087d404430414ff394b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_types\\rx_iterables\\rx_set.dart", "hash": "ae4e66d6475ffe205a9530858201845c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_conditional.dart", "hash": "3e06f0d1bccdf76baf4f4e0fb4868c84"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\constants.dart", "hash": "38a87ff489a47bc024400dc863be326d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\code.dart", "hash": "2d312745691a82b398796ad2f38ac63c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\hive_error.dart", "hash": "705c71a4fde7fd9f2f8130b35b98caa5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_extensions.dart", "hash": "903d8536aa6c9e6926e96e9a2b449824"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_ios-0.5.3+1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\common\\tuple2.dart", "hash": "48d0bfb3ff26eb6a74badf5cce3531a9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\agents\\agents.dart", "hash": "8b67847e70aec8d987e864fbc3c31443"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\rx_flutter\\rx_obx_widget.dart", "hash": "a0b61fc85492da95a4302dfad622c061"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\registry\\type_registry.dart", "hash": "c17abfd46dd4cb9d6b286b913754f6fd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\platform_views.dart", "hash": "e5f1c75356cc6475991cf10f946102b2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\select_all_recursively.dart", "hash": "f44a1e05d1daaf19e520f9050a2e1057"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\LICENSE", "hash": "2f25516eac3c3053c221748de0b14cfa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart", "hash": "97f8d480ec6ac1778506d29f498a1e6c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\loaders\\csv.dart", "hash": "be363e4075ef91c73f92cabb74320d8a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\screen_capture.dart", "hash": "a7ca311b68f6ea52b0980d9f502fb6d1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\version.dart", "hash": "3b6099d561a936367122fc8dd8b5314c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\matches\\matches_iterable.dart", "hash": "037df9e7342fc8b812d985c8b6e8a0c3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_compressed_texture_astc.dart", "hash": "6f4f3b33b7bc8ecd9ead21959e169f7d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\sequence_equal.dart", "hash": "e9e452fa340b489a49dba00eabefa3ba"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\loaders\\base.dart", "hash": "bbc85b8a694bbfabcbe5f09b7ce23574"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\openai_dart.dart", "hash": "0d9ce27cc6422ab4501a83947edcdbf0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\oes_texture_float.dart", "hash": "d5f7267a21029dd081e33d87f5a0661e"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\content_review_service.dart", "hash": "3c6a36daa4a0059a84329bef240099d1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart", "hash": "8d5d3ccddf53eafd7a3094278afe8b93"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\interface\\tree_searcher.dart", "hash": "9a70643d75678b36991aaae52bbdda6f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\async_cache.dart", "hash": "638c6d804d20c1f83790f7f10c4af408"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\empty_block_syntax.dart", "hash": "c7d152e4252df29c816282634b4e6b11"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_4.dart", "hash": "54d72b1c5b9977ccdcb6cd95e8acc7e6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus_platform_interface-3.4.0\\lib\\method_channel\\method_channel_share.dart", "hash": "a27e281f7f3b1a2b17fa82a3e3bc0421"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart", "hash": "deb3149757b677ce04ef9df630a6f268"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\pairwise.dart", "hash": "e8e03ace330da6d410583717e7e5f681"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\cssom.dart", "hash": "fe51ff1e9287f5f07d9e0c75a95ce011"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\novel_detail_screen.dart", "hash": "afd9b2029158eb5d9608284698a8d392"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_core\\get_core.dart", "hash": "0760111997c8cba31f755dd6c0f461d6"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\novel_chat_service.dart", "hash": "9bdef4326c10846300123e83ef670015"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\crypto\\crc32.dart", "hash": "21913fbf147ca790e444082cf32a7c84"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\lib\\src\\bound_multipart_stream.dart", "hash": "8e2dfba570ecd1895c50258939b609a9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart", "hash": "2796f86b16cde28eab42232889d082ed"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\char.dart", "hash": "00456c7fcfc11e9ae46af126277652d5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webxr_hand_input.dart", "hash": "97f94ad53103b6813eb26a6d64910efa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_properties_values_api.dart", "hash": "220c3732a923196f9a41b6c327dc3fe4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\theme.dart", "hash": "5c5498b4a09ed9402b1802a7db4536ea"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\reflection\\iterable.dart", "hash": "763f95cfee7dc8f35f6557eab7e94312"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\parser.dart", "hash": "668feba83ac51da82a0cd90d035b271b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart", "hash": "8a5f786f5d88821fda1f83bc51501a31"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\submit_tool_outputs_run_request.dart", "hash": "d1cf9260cf3f8dde42744e8a294fd706"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\src\\_internal\\encoding_mixin.dart", "hash": "dad0583ae4f0d37f4389edbbb600f2b2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\LICENSE", "hash": "ca58010597a5732e6aa48c6517ab7daf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\markdown.dart", "hash": "e2bffaa3cd7572a5e1d411a8deb8ed45"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\web_animations.dart", "hash": "ce0df8c9dd9f2b269d63313b9ed06d24"}, {"path": "D:\\element\\flutter\\packages\\flutter_web_plugins\\lib\\src\\plugin_event_channel.dart", "hash": "5b1024445426f8a8b1613eb98a0272e5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart", "hash": "230a3518091834c1ebaba0eda6ad491e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\constants.dart", "hash": "df0a9878a16d3cd73cff00f496307544"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "hash": "7eee695ba96e5afa80abfaf59973617a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\debug.dart", "hash": "136b08c4413778ae615af5f45d39ed93"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\separated.dart", "hash": "641f0dfad31a545ac6fa5515e83920fd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\src\\types\\types.dart", "hash": "f4d93b039bc86c4a156848d06fbc2917"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\error_and_stacktrace.dart", "hash": "491a33282b614f40bd0fbd3f3b3d45f1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator.dart", "hash": "7e37882cde626ef5e8f5401273abedd0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\math_expressions-2.7.0\\lib\\math_expressions.dart", "hash": "4c10d430e9470d6d1edcd5770866804d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart", "hash": "d773ee48068ac90b654336d1ec93541e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\binary\\binary_writer.dart", "hash": "61da4ed39b7ee4b0a5256d7c7fcd0a61"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart", "hash": "994fb9ad204eeea38bdc0040b37283f2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\constants.dart", "hash": "310649eda1830de4f669332f93865282"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\lazy.dart", "hash": "128e022b683572b60bce0c93cd05007c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "build\\web\\flutter_canvaskit_config.js", "hash": "5e958eed053e64108c20c8e604da8ad8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\fun\\fun.dart", "hash": "36a0aac77425ebf5cd78db3aae2d52d0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart", "hash": "e4825ae07a44bfd1b203bb9522099e67"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\word.dart", "hash": "05e847132bc525d82c8f22363faaab59"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\default_if_empty.dart", "hash": "527ad391c229e34074a6d5c1aa656133"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\llms\\models\\mappers.dart", "hash": "fcfa6a43022d2f2dbf7da2d339d254e0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\mediastream_recording.dart", "hash": "45a6578b2c1f76cf920d26071875cc45"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\util.dart", "hash": "c9a4f1c8cf8e652db3ddf2506c36b026"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart", "hash": "e11d89b7d2933ba28814915b300af866"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "build\\web\\assets\\NOTICES", "hash": "fa946d3ca754995c95d95cb076a88916"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\css_highlight_api.dart", "hash": "d7811ad2469eaae161434b3d6d29d375"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\escape_html_syntax.dart", "hash": "594ba66ccacf8fee1072352a50a9cf0b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\utils\\labeled.dart", "hash": "715bccb8e9ba9889573a60bf0e457402"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_transform-2.1.1\\LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart", "hash": "60c5da3ebddd0b8375127280e8813c4d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart", "hash": "413144882e92d0a27858427f45f214b9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\image.dart", "hash": "3c37d0f6265f2f3546d91011809bb8c0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\ovr_multiview2.dart", "hash": "4f4be543ee7b471b82757e405a2e9356"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.8\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_parser.dart", "hash": "4150b03eff275c390f0552a3112b3efa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\readable_stream\\readable_stream_default_reader.dart", "hash": "4896f0868e0e9ccdabeddbabc6feb641"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\material_localizations.dart", "hash": "fa2e8383a63d2e8430ba5ec6b81175d0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart", "hash": "cb9617d35408474cec5c44f6d57c0faa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\js-0.6.7\\LICENSE", "hash": "bfc483b9f818def1209e4faf830541ac"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\normalized\\index_selector.dart", "hash": "3145fce54927ad8830c2e26bf2a5bcce"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\binding.dart", "hash": "7474b32f63cc4b2990f0b82fb4937525"}, {"path": "build\\web\\canvaskit\\canvaskit.wasm", "hash": "efeeba7dcc952dae57870d4df3111fad"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\web.dart", "hash": "6d61c054b2c590f89f518959b29a2002"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_core\\src\\smart_management.dart", "hash": "802d0a87566ff7328fad39a41ca9b45f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\freezed_annotation-2.4.4\\lib\\freezed_annotation.dart", "hash": "e0edb55e8495e5c2208d8505329333cc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart", "hash": "fb5592ffbdb3669d56c8d1cb23ed3033"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\rx_flutter\\rx_ticket_provider_mixin.dart", "hash": "0090746e115de617143d7953e8ef2ef9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\lib\\share_plus.dart", "hash": "07d60aa100b4ebcf5bd603008d425e91"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart", "hash": "8a8f9aeb0001ca5b48ca4ea93a0f4831"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\lib\\src\\web_helpers\\web_helpers.dart", "hash": "bb9e04644b6d2ed527d5df1b8523dc85"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request\\request_referrer_policy.dart", "hash": "6157c755b4ca5e8c7f0ad0d48944c141"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\json_path.dart", "hash": "295113e2aa281004277237818fb88b8f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\cast_list.dart", "hash": "87751ee02d315bd2d0c615bbf2803a3d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart", "hash": "2fe41384c97b732ca4986150ae7a002e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\expand_icon.dart", "hash": "d240d82f159a7c1d6b241bf236eb26a7"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\background_generator_screen.dart", "hash": "9892b1eb212a7d9c81239e85e5545b05"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart", "hash": "4a77eafe460177c2a7183ec127faabff"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\container.dart", "hash": "de961c25d71b7a769062627541bfbcbd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart", "hash": "dbdb73fcbd1f5bef4680a288af14888c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart", "hash": "1ea047f0b3833f762bb5bff4a50d7707"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\stores\\in_memory.dart", "hash": "b4f333755f83043f82b1ee6b5544506b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\simple.dart", "hash": "b25d39ed4dcda5a9c7ad6624683bfcfe"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart", "hash": "b2eb657328bd482e6b083d76dfbca76b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_step_details_tool_calls.dart", "hash": "fff9ac30be989440f338067fb8b99310"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart", "hash": "a0847dea6404a5a2413fb2690a3a614e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_extensions.dart", "hash": "3a2d505268f5446e5f7694776b69b407"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\array_slice_selector.dart", "hash": "cf9940d2aa21e9969cd02cedbeed683c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\score\\score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\block_syntax.dart", "hash": "94eb18a0875127b448a86af4fa23b3b7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart", "hash": "09bb22b1c2675b310a94dd1d4e8d6634"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_view.dart", "hash": "3bc911080851a26c88432f25cc6362dc"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\character_card_list_screen.dart", "hash": "5ff24dc165592c14eebf01bcfa7845d7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart", "hash": "b287a6e9ffb616da8e625bd652053b55"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart", "hash": "de8af1fa483044ab0db257bb9e716a6b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\langchain\\utils\\model_adapter.dart", "hash": "a8d9d86d222d11478b266de0ea672860"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\selection.dart", "hash": "4171ccc0ef5ccf7a3355589667cc0450"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\simple\\get_widget_cache.dart", "hash": "eb1b7fd1971730127f0d15256e4c3955"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\llms\\models\\models.dart", "hash": "670cce4c9e1dfe24e5f9a0b47f19cb17"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\object\\hive_storage_backend_preference.dart", "hash": "bd95228b199ffc9f775bb4e037a461ca"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\error.dart", "hash": "056ba78280a44883e05c65a88771b4e8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_compressed_texture_pvrtc.dart", "hash": "96ea44a3916958ce0ae07a66485cb12a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart", "hash": "e39c804b77ec1bf1f6e4d77e362192c1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\shared.dart", "hash": "443556370e0497274bf7d9842a0eee84"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_boundary.dart", "hash": "f2045075d36258ce6ae2e6be59b46fed"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart", "hash": "7c9757d3cc07fc4355bb72187af0413e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "D:\\element\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "hash": "ebc6759fa73c53bc12d581d9e1e4c821"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart", "hash": "0fbda02660f6ca3d9bb5b9ce5ab88548"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\simple\\get_view.dart", "hash": "9b56ea3a6f44ac4fdd5198c39f1f9d38"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\schema.g.dart", "hash": "d22dd88901ec11677e7b39b051b30f74"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart", "hash": "ede4471174c160615d5a7dfde9b78362"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\object\\hive_object_internal.dart", "hash": "1d6b06c440ce770d590ccc694f67e7de"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\rfc_6901.dart", "hash": "967727e642859e9ebe3da350edff2aee"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\lib\\permission_handler_html.dart", "hash": "02e6dfb65eefb07c08da31829bc9db53"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\byte_collector.dart", "hash": "3aaf04a3a450c1b6a144f84f3c778573"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\method_channel_url_launcher.dart", "hash": "351ed98071b53d3c2e98d376f2a65a74"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request.dart", "hash": "fce8e2eb75ae6f61d5ec322d31c03c1a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\yaml-3.1.3\\LICENSE", "hash": "092362603d55c20cda672457571f6483"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\failure.dart", "hash": "2db6cf613a3f03e05a2c19ca6e14447b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\inline_syntax.dart", "hash": "93454e8a4f0b62b8c72252b732c6fe36"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus_platform_interface-3.4.0\\lib\\platform_interface\\share_plus_platform.dart", "hash": "41a8a6c9daee486dedb0afb27b7766f8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\agents\\tools\\invalid.dart", "hash": "a7e4675f752eee7d4cabe6132f331475"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webrtc_identity.dart", "hash": "d41bf06a3f15451f68bcc24768c5c5d5"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\module_repository_screen.dart", "hash": "42492582fb252482e82f8418eae12fd9"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\controllers\\api_config_controller.dart", "hash": "c997dd193b2f117c52073af8113f16f3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\auth\\profile_screen.dart", "hash": "1b0506885654fd5df2b250a745999991"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\flutter_backup.js", "hash": "e32f874a0fff1dd87a200aa8ac22f487"}, {"path": "D:\\element\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\agents\\functions.dart", "hash": "c439ff2a985cb998a154306ddb649f89"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart", "hash": "869bbc5dd32224e5adaeea14f49a95c3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\client.dart", "hash": "48e477bd260cf4d0d31c15a34a0b5d9c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-7.2.2\\LICENSE", "hash": "ef2f4f5048c86bfd71a39175b6f103d5"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart", "hash": "d30c2a00baf926b19614ff13a256ade8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\src\\csv_parser.dart", "hash": "7e8de88636591fb00d3cbce8857d54fa"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart", "hash": "03e34b6476f2a5b43080c9fefcefe9ea"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart", "hash": "828a01a48b4a0451b0d1f9c531cc292c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart", "hash": "861a19ff01e3f58d95d668b9fd4054f7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_client-1.1.4\\lib\\src\\fetch_request.dart", "hash": "d3df41e491c2afbf3e4d0c0be4dc942e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\tabs.dart", "hash": "7db257638ae04e779fcb8984a35ccdf1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "build\\web\\canvaskit\\chromium\\canvaskit.wasm", "hash": "64a386c87532ae52ae041d18a32a3635"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\continuation.dart", "hash": "33717fbf3f4de35b5e494d284a252bb7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\language_models\\language_models.dart", "hash": "351696e0412b84b804d6603cdc807fe3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\lib\\src\\int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\number_symbols.dart", "hash": "6c1b7903629a7ad4cb985f0898953db1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\predicate\\string.dart", "hash": "be2e3e8ab6ed0e2b2b554a26b78f91f0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgpu.dart", "hash": "bfaf083479abcc6fad1aac4531783dcc"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\output_parsers\\utils\\json.dart", "hash": "aa96e3653fe5aa522bf17d2ecbd906a2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\fine_tuning_job_error.dart", "hash": "fd174781d511a60bed21ea03dd4c72fa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\indexeddb.dart", "hash": "69a74463ae4c417d0084353514546c28"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\main.dart", "hash": "305413969b2d0344045a199e876cedd4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\request\\request_mode.dart", "hash": "957326ae8b9fd5796e975ecd42f368d1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\concat.dart", "hash": "eb8f82998d7328c46b04354df987a331"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\widgets\\common\\loading_overlay.dart", "hash": "553f91a754f3d7ffae60064ffa6fea07"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\stores\\stores.dart", "hash": "8855174b105010e62a19bf684db0cbf4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\epsilon.dart", "hash": "b9283cabc57ae94b3c75f147903751fa"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\multitap.dart", "hash": "8088a1e9032c8c67b16c9d46d58329fa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\sphere.dart", "hash": "d1089412c69c2ca9e4eeb1607cf0e96e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\oes_texture_float_linear.dart", "hash": "c7027f3f13166997500119a5cc6e3732"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\form.dart", "hash": "d032a04dfa5ce10249fe6ce2abbdd222"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\aabb3.dart", "hash": "4d9f681599b9aba645421097eda46139"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_storage-2.1.1\\LICENSE", "hash": "3ba63c94d27987c4ec370afaa5537793"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\LICENSE", "hash": "3ba63c94d27987c4ec370afaa5537793"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\footnote_def_syntax.dart", "hash": "ea281aff88e8f6d6fddcf792c8a0a56f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_step_details_tool_calls_code_object_code_interpreter.dart", "hash": "ad9ebfc33eb05482ba9c0b9e115b3ba2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\start_with_error.dart", "hash": "47cb151906114ae0161078bb7968ffc9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart", "hash": "55df38a6fe2ab71614693edc6ef280f8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_state_manager\\src\\rx_flutter\\rx_disposable.dart", "hash": "e72882ca45931abb87cae04d914b2970"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\chat_models\\fake.dart", "hash": "89b1f38aea1d8e1cd6c1ec791ebe07f1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\fullscreen.dart", "hash": "8ce1ef239f773dbbb83a136ef8da4560"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\ranks\\gpt2.tiktoken.dart", "hash": "47661200862f2e6f9b202b4e592e1298"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\extension_set.dart", "hash": "e9a5827d0484a37aecf244812d9accff"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\lib\\src\\default_extension_map.dart", "hash": "fe2df60ed5b05e922df2ee9fef5cf5d9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart", "hash": "5588e04d2462fa3c03dc939826b0c8d1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart", "hash": "d2f2b0176f8e5db06daabab8e6a3e5a6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\lib\\fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\hr_time.dart", "hash": "b48b79ddcad91a15f6ed332a695af619"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\retry_when.dart", "hash": "add862853473647f3bae9dee0b365857"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\import_screen.dart", "hash": "e7ea8f1845b026227ac49f957838c6c7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\min.dart", "hash": "ecfd8a09746c8bbb7b51d4741fb4645e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\system_navigator.dart", "hash": "6a7f49ff645804c67a62656a0046ca5d"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\platform_channel.dart", "hash": "62f20a61fcca0d9000827ee5e54a30f2"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\controllers\\genre_controller.dart", "hash": "d6fca9733a03b481b6659515be4f670a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart", "hash": "f8867a31bedba73738dabade00f04fea"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\picker.dart", "hash": "e0f8ae49e5195d1949887769d1e6117d"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\utils\\text_splitter.dart", "hash": "ffaeaa343dda8fa8616bb4dfd15d15f9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\prompts\\prompt.dart", "hash": "2a4c1697572a9815de5e1527b350daac"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\number_format.dart", "hash": "c4a5c35dbaaf6daebc97eac0f89b1f68"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\pattern.dart", "hash": "2108c716fd8198fa3a319a1ec6cadc9d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\transformers\\text_splitters\\text_splitters.dart", "hash": "466409bbe691a5ca49891e1cd76bbc93"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\visitor.dart", "hash": "9cc453290a0fea4e24b848a74967c59b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\lib\\src\\ranks\\cl100k_base.tiktoken.dart", "hash": "81cedac9ac247dfcb7bb76339bea190e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart", "hash": "42919d2b803fd408229c56001114a1d9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\mediacapture_fromelement.dart", "hash": "456edf48718a9d59a2fa9b7e937a986e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\physics\\simulation.dart", "hash": "0b594cddd30fe9d0d34a42f5328a2b38"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_completer.dart", "hash": "2430a12d4750c3c76ef07d29bb6f6691"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\crypto\\hive_cipher.dart", "hash": "a2716332bd9726a3ab118d6fd896ac17"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\LICENSE", "hash": "e5c509c88ba5deea93f23433c98752c2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\image_capture.dart", "hash": "78a1afefd2a717b10332140d9a709e6b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\chat.dart", "hash": "70ef322d88413005fe82a6fd980ab928"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\core.dart", "hash": "ea3af9e2950c2bfc6997aca644d72229"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\svg_animations.dart", "hash": "b23ba9698be55510ef57051143f4d8b4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\card.dart", "hash": "1eb9cc478ea7bda3219d6e73edcb2929"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\delete_assistant_file_response.dart", "hash": "3ba33c85b49f94bca09544ee4e768096"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_debug_renderer_info.dart", "hash": "4155ef1accbeb110c862d616f2a2ad3a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\hive.dart", "hash": "3e6bacd9c2e1cc522a82a8b3a3c7f713"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-11.4.0\\lib\\permission_handler.dart", "hash": "ae9b498a0c3fd784a628e57eb92307aa"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart", "hash": "9d30bc3c060295dc4cf82d7f3dcf74aa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\tiktoken-1.0.3\\LICENSE", "hash": "36740d2104b219636682df85767bc9dd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart", "hash": "823b3b44cc184688e1fd926c923ada17"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart", "hash": "ffe5ffa81a95dfa29637da7f638fffbe"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\event_timing.dart", "hash": "303647c527ea561eec5969c76138b1e2"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\patterns.dart", "hash": "ab815481b9d8cc39fb1ed5327a4d1d8b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_converter.dart", "hash": "2d069a48b5e0ffa386474977d2c91c90"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\internacionalization.dart", "hash": "27e63500225072bdff1af269d1b9d003"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\retrievers\\vector_store.dart", "hash": "064f317eb52a09b3c62320d34e620a5b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\base.dart", "hash": "eaca46e4233841376bfcb8fb0ca8a545"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_assistant_file_request.dart", "hash": "44df07726d6a32126ffbc6c1b1077ca9"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\novel_continue\\novel_continue_screen.dart", "hash": "62baea534b00cac16e23178d677803d4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_stream.dart", "hash": "3473bd2c623a639ff8cc439276a3825f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\binary\\frame_helper.dart", "hash": "cb79a30b4326b1cbfb62680949394769"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\digital_identities.dart", "hash": "8ffb32766ef04667cdf8767229bf2696"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\config\\api_config.dart", "hash": "1fedfd5b4c64c6ffe9e88e445589c53e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\feedback.dart", "hash": "65d7d9aae72d673e52ab256ebc374bf5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_generative_ai-0.4.7\\LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\inline_syntaxes\\color_swatch_syntax.dart", "hash": "0f067afef7d8b533ead091233d32dace"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\runnable\\passthrough.dart", "hash": "2e90164196074e8fc8114b6723088d31"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart", "hash": "1fbfdb508cbaf318a89890747d632a67"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart", "hash": "833549c8f32a60f8bc7d2f221760844f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\expression\\static_expression.dart", "hash": "0ac836e58ebca049e3a224b9dbfdd048"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\utils\\resolvable.dart", "hash": "f7329cc0811af555900320e49bd9686f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration.dart", "hash": "6da099b31166c133a52bfa3ab7a1b826"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\language_models\\models\\models.dart", "hash": "7bf8375e59ce019747bcf36ab23144b0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\tags.dart", "hash": "e4880e8f7024c22987351abefc06f5dd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart", "hash": "114ae80ca25cafb0d4882b55002ceb6e"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\.dart_tool\\flutter_build\\b7a4acd900463a69943b557d4d9a654f\\main.dart.js", "hash": "10b18a115a745f13339608a8ac6de87e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\filter_effects.dart", "hash": "3cd49043e01257e2a2bc66975e708b02"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\controllers\\novel_controller.dart", "hash": "79286bab8c40c15ba68f37f8c405da0e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\embeddings\\openai.dart", "hash": "dcf90e529e4f084d02af83979a93c81c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\get_rx.dart", "hash": "380c75c51553a3f9ed18a45e82ceecc8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon.dart", "hash": "68c2698d9480a0bf5a6211ab2145716c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\media_source.dart", "hash": "19e9e75b805121b8f916a22696c1d82e"}, {"path": "D:\\element\\flutter\\bin\\internal\\engine.version", "hash": "4a414bd4083c489617af112624a0a365"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\picture_in_picture.dart", "hash": "ccc4239831a5ea14583942ebea81a7a3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\utils\\forwarding_sink.dart", "hash": "89e6e01bd627dc1cc46b16ae027742dd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\iterator\\extensions.dart", "hash": "bd2f31c7971fbd83c8577963e327a558"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart", "hash": "d29e9119669959c9cc3eba800cc79d90"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\core\\parser.dart", "hash": "830859b7bec94f5f922eaba151827455"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_promise_or.dart", "hash": "ca0b72171b1b0fc79afbca9faf826f83"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\controllers\\auth_controller.dart", "hash": "de415cbad6599a65a22404702c56888b"}, {"path": "build\\web\\welcome.html", "hash": "77373ec2ddeae5c82a94586196adfe14"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\media_playback_quality.dart", "hash": "6005946ba650c618c2eace5c1f999212"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart", "hash": "13bceb5508fcefacc9ed46137d43844e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\agents\\tools\\tools.dart", "hash": "1b7f90e4d5cd2848654b23e4f610de7d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\time.dart", "hash": "606636e865b06ca9bbefe3560a32fc7b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart", "hash": "4caf5b7962b2f058f4c5e33d69bf7ce7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart", "hash": "1b874f643202dc2dbc5b8e89012b2ac9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_stream\\get_stream.dart", "hash": "a748deb181ad41928c0142c843237545"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\material_button.dart", "hash": "61e660e12e1c2bd4b570416482c8f18f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart", "hash": "47ca12334beafc625a5b88d0ce2b0e87"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\badge_theme.dart", "hash": "8ba398f65bba2f40abf54b600e709975"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\debug\\conversation_debug_screen.dart", "hash": "98c941a0146e5482ca76a8a69544d5c2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\transformers\\text_splitters\\character.dart", "hash": "56d4e1290e361e32c384b0751933d75b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\auth\\register_screen.dart", "hash": "ab5c216dab30cf16582da2ca32a21c8c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\create_chat_completion_response.dart", "hash": "a099c6fa2434bad553ef8bf9ec8c90d5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-1.0.1\\LICENSE", "hash": "274291edc62b938ad94e61cec4a14bec"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gbk_codec-0.4.0\\lib\\src\\converter_gbk.dart", "hash": "67d8be757166def18bd8f002f46329a2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart", "hash": "********************************"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart", "hash": "e0d86b7f5d94eb97a3a1f9393dd6b817"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gbk_codec-0.4.0\\LICENSE", "hash": "1ed1a2d664bacf3bf5491f7731fa2b10"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart", "hash": "6ee7307afd79f7f32910b4db973200fe"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\transformers\\text_splitters\\code.dart", "hash": "c7aa847a6c560c828a96a4a141d399df"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart", "hash": "2aefe5765514a4cf4b2319073b11a0bb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart", "hash": "f31cd1126983d313d533c2f530bd1c33"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\schema.freezed.dart", "hash": "85fe42bb41aaf47d4ce73256216f0f76"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\routes\\circular_reveal_clipper.dart", "hash": "972639c4c0feb70173d454e6256f22c1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.3\\LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\literal.dart", "hash": "29e433fe023f4405404e319e40e221c9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iregexp-0.1.2\\lib\\src\\ext.dart", "hash": "b95ed4a1acfcdbd9ecd28cff0ce7d9ae"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_value-8.9.5\\LICENSE", "hash": "b2bed301ea1d2c4b9c1eb2cc25a9b3cd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\semantics\\debug.dart", "hash": "0afbdd6f1125195ec28ff55922e51d50"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart", "hash": "7eb989577e5ba101feca9c465b25264b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\uppercase.dart", "hash": "7061b91c27425c907020fe54e569b9db"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\src\\csv_argument_errors.dart", "hash": "396b9385928c724b89bcf70085607157"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\intl\\date_format.dart", "hash": "e0e1b6343cb4c49cf84e50e7bac742c1"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\lib\\v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart", "hash": "aff356351126de3409e033a766831f87"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\types.dart", "hash": "ce0d3155596e44df8dd0b376d8728971"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart", "hash": "04958562cca050e70dfde058bc4c8b3c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\dom.dart", "hash": "6a64fecc9f1801803c9a6706f60ad958"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\json_path_internal.dart", "hash": "795aade316756975150b66fdb6d4bee9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\get_utils.dart", "hash": "e0dc3449b7b79024781e1c216d68db96"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_step_details_message_creation.dart", "hash": "f35f790abac9171f0e0177ede7034018"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\just_audio-0.9.46\\lib\\just_audio.dart", "hash": "7b8a0192de0b5cfc351ba6b4bfd87233"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\text_boundary.dart", "hash": "8ebfd4103b3ffc0ad516a44a2ee40748"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart", "hash": "974132eb99786ea123adf0a96081f93b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart", "hash": "21cd7575e7465df0bea2705f0421e752"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart", "hash": "fe53f754a8905eaf7ccb7d3007ec8ab7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart", "hash": "59ad3f592944fc890a2333c208b755a8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.2\\lib\\method_channel\\method_channel_device_info.dart", "hash": "811012c24b5b0bca98b118bdbffbfccb"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\chapter.dart", "hash": "f466d546281171258311d00a2ba08268"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart", "hash": "f8ded7a3f53ded600055288202a36535"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\concat_eager.dart", "hash": "28788651dbafca42ae0d6023352274f3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\colors.dart", "hash": "f7c00541a5a491e0984b4a303192a51a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart", "hash": "40a04e94b4e68da57cf200df6c661280"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\character_generator_screen.dart", "hash": "4a8045aa29c48495cb207951818a2f60"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\lib\\file_selector_platform_interface.dart", "hash": "eeb75628a0a17d5d8b5dbe0eafc08a29"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.18.1\\lib\\src\\global_state.dart", "hash": "d1476acb9e8c42fb16e1c26b03b06340"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\images_response.dart", "hash": "3e8f290d246f5cdb783c9fbed00de116"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_utils\\src\\extensions\\num_extensions.dart", "hash": "d16f3e5514424ad80bc1af044511326b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\button.dart", "hash": "eabc0ff413b2fef64e2d0ce3554556be"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\agents\\tools\\calculator.dart", "hash": "86d0da0adc13e722bfae9565a66e9369"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_connect\\sockets\\sockets.dart", "hash": "4ebe119789d0cd8acb6740e81c19b49e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\extension_navigation.dart", "hash": "5d680da4b6a4042f21f18c19c9c78454"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\src\\list_proxy.dart", "hash": "d1c07a46157914ec4aaa9aa9a957df37"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-1.0.6\\lib\\src\\char_code.dart", "hash": "4fb96b9e2073cadc554a25b36f55e6dd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\iterator\\iterator.dart", "hash": "4e4412769d996a1b6f62d8f1e76caad7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\standard_component_type.dart", "hash": "702ebe43a77fbc5d1e1ea457e0bce223"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\message_content_text_annotations_file_citation.dart", "hash": "25ee760b7dad7d11b5f822b2ea2b562f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\chip_theme.dart", "hash": "ee424e7e1baf8744e2386a37728238fc"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\material.dart", "hash": "66b3d370b349777f8860882dbfb17cdf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\chat_message_history\\in_memory.dart", "hash": "dda6cb844be6f7899720fa11623e792c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\.dart_tool\\package_config.json", "hash": "4c8304053ae2b5709b41b977daed6c3c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\push_api.dart", "hash": "c4a77ece416f851e2b69b7a57136bf4c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_workers\\rx_workers.dart", "hash": "b8830d02401a1999081779d294ee8001"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\shared\\annotations.dart", "hash": "9a469ff3de60c96cf2f9b0523b651782"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\rx.dart", "hash": "f222f3be7d9e176a7d8ba3252825c9f8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\date.dart", "hash": "f427a21677c71d2ae0e424a6657c50d3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart", "hash": "d0a86046f0bc5262b7752d63d91a5005"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\character\\digit.dart", "hash": "ea08cf8b71713e3535d5a2164a8bc7e1"}, {"path": "build\\web\\canvaskit\\chromium\\canvaskit.js", "hash": "34beda9f39eb7d992d46125ca868dc61"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\completion_finish_reason.dart", "hash": "7e1a2bcf33e88a67d98851e9f8520375"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\delegate\\stream_consumer.dart", "hash": "987dfee9ed944d2007a00e521d4fbbe4"}, {"path": "build\\web\\download_canvaskit.py", "hash": "6cceae68480ed769cbce9cf0b4b9f55c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\binding.dart", "hash": "81d88149319fdba8c35cd4ea3a44a611"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box\\box_base.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\client.dart", "hash": "81269228db4b1b2466b5590eac7a746d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\pick.dart", "hash": "c60b204fb5e7d501c0addb330c88d2de"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csv-5.1.1\\lib\\list_to_csv_converter.dart", "hash": "0d6484fb74e91b733cc8a4d4da125d33"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\clip.dart", "hash": "14a19b57ccb4125356c817199d940c55"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\markdown-7.3.0\\lib\\src\\block_syntaxes\\ordered_list_with_checkbox_syntax.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\max.dart", "hash": "82294310993896043f681e7fd66c4e56"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\lib\\src\\formatters\\string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart", "hash": "2ced57b0fa711aca80714d917cb75cb7"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\range_slider.dart", "hash": "0a6bcc34744ff2baab5ba9181648b1bf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\pointerevents.dart", "hash": "81f93ab4890d03a269bf7927aa31cd7a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\web_share.dart", "hash": "b741e14cacd655b8d9ce8fb1ed1034b7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\chat_session.g.dart", "hash": "76877155e2ee33d5625a8372bfc6835a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart", "hash": "6ee6824627b335ef6ff1e8f75aef75e4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\predicate\\predicate.dart", "hash": "ec001ba2712f91cadae858bfdfe622e7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\interface\\tree_modifier.dart", "hash": "c8694d497996d97c8f6610e196d0d6dd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\legacy_api.dart", "hash": "e62a8f39ad332b5e313b0be97f2d280f"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\lib\\path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart", "hash": "4988a75e1b6c9b1b2c2df65cd6318bf4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\chat_models\\chat_models.dart", "hash": "99279d8e3aa087cc38276b934dd278de"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\js_define_property.dart", "hash": "bc452347f9d6f28131bd84840d1a496e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart", "hash": "375378eb6cf9297d6bcfe260059193a8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart", "hash": "b61a4dbd857b74aa723c96fc1b53fa90"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\chat_completion_response_choice.dart", "hash": "58adf54e0cb20132acc1248950a6feff"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\freezed_annotation-2.4.4\\lib\\freezed_annotation.g.dart", "hash": "e9358b84882d088105b22ba78da5db6c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart", "hash": "0071fe298793e366f6f5b16077adbf4c"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\motion.dart", "hash": "c2593e65f1a2367d83f0668470ab5f61"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\take_while_inclusive.dart", "hash": "389552e6852c3214ca6857ddadb7cd0b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive_flutter-1.1.0\\lib\\hive_flutter.dart", "hash": "ed6800e3fdfd2d724c29415c77a47dc4"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\import_service.dart", "hash": "9e2f1bc07e44f2d2d6a0f715e663e4f2"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\build\\web\\main.dart.js", "hash": "10b18a115a745f13339608a8ac6de87e"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\screens\\writing_style_package_screen.dart", "hash": "c178a5245c9db6670179aa800b897fec"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\message_object.dart", "hash": "c18da2c7ddf80ada84698a8b94df8a00"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\debug.dart", "hash": "203d46859b027ddaea624814ff36aab4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fetch_api-2.3.1\\lib\\src\\readable_stream\\readable_stream_default_controller.dart", "hash": "311b43709e03d394f217396016cec3db"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\physics\\tolerance.dart", "hash": "a481b9285f5d63f04e3b3e3fc2a6b44c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\stream_sink_transformer.dart", "hash": "8117e1fa6d39c6beca7169c752319c20"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\timestamp.dart", "hash": "bca54a6e3c0b80a2300ab9ae4e9db4e9"}, {"path": "build\\web\\DEPLOYMENT.md", "hash": "465c4a09fda200f469cf467658f8e233"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\plane.dart", "hash": "2a0078c9098cdc6357cbe70ce1642224"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\combine_documents\\combine_documents.dart", "hash": "d10119f4e6daf435d2d6a7fe5da61a4b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\iregexp-0.1.2\\lib\\iregexp.dart", "hash": "76d1fda2a6c1c13903ed08913e3b543f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart", "hash": "c87e92035314a4d3e52faf886355d0a9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\delete_thread_response.dart", "hash": "5caa650ea7872a410ad9816d746b986d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get.dart", "hash": "17c8d20a7395bb3a3a09a8922607786a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart", "hash": "2e9c69c640f736eb6c9c81c3238ec910"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\audio_session-0.1.25\\LICENSE", "hash": "fba1d88f08129f8046dd091b8abb5ded"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\novel.dart", "hash": "f0c882662af982bd050b99665815b7a0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar.dart", "hash": "5963af31f4a3213cf4ce04d2b1ca7eb2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\button.dart", "hash": "169085ebe2938e16b6df397213ee7650"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\viewport.dart", "hash": "9d93cea34a3d8324122b5becaedf54fe"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\image_stream.dart", "hash": "b47c0bd8cbf5da61cb90fdfebfc65915"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_step_details_tool_calls_code_output.dart", "hash": "293e7a92264089a4a3f352b71734f3b3"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\table.dart", "hash": "6211042590b5ddf56b0b4123a8249310"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\basic.dart", "hash": "5e74b57377cbd2ed562de61389f454f4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\chat_models\\models\\models.dart", "hash": "6db7ac1ad6eebb0720de0603dbae9990"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_collection-5.1.1\\LICENSE", "hash": "b2bed301ea1d2c4b9c1eb2cc25a9b3cd"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\largest_contentful_paint.dart", "hash": "422496814972d30f353aebfaa10ba3ac"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\delegate.dart", "hash": "5fc1a25f60cfa0a0280878377348c63c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics-1.1.18\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart", "hash": "798eca841462fc1fb5250cef00955063"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\parser_ext.dart", "hash": "018218e6dc58f2dd3aebcc07872d449b"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart", "hash": "********************************"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\null_stream_sink.dart", "hash": "cc0ab0117e8a0a54ec3efe6d9251860e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\subjects\\publish_subject.dart", "hash": "b2fa768bd42261fab936524dd6f1c8ae"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\services\\conversation_manager.dart", "hash": "22fc4d92a4fecb0f74ff732ee7ef31c2"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\autofill.dart", "hash": "900672c4f3a8c395331517ee4a59ea2c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\llm_chain.dart", "hash": "4b9e010508f16c06071d91e1233266fb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\box\\box_impl.dart", "hash": "********************************"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart", "hash": "734c0e931a329ed28d4016070d47edcf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\icons.dart", "hash": "9f7270100e64dae8750b9ae54cde56e4"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons.dart", "hash": "d8a7b96e6465831f5cf6de56ccddf6b4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.7.0\\LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\throttle.dart", "hash": "12faaaa2952e6917c271f5dbe9cd6bab"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_core\\src\\get_interface.dart", "hash": "06d49e45784746e76e310c8e99e5932b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "build\\web\\build\\web\\assets\\AssetManifest.bin.json", "hash": "99914b932bd37a50b983c5e7c90ae93b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain_openai-0.3.1+1\\lib\\src\\agents\\tools\\dall_e.dart", "hash": "769944fd9985a8f577f9ccb4a230bfcf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\grammar\\fun_name.dart", "hash": "5b425dcacf1de3797d618dbdebb4eddd"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart", "hash": "d17cba679c8b1c94e10dfe2419b3d581"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart", "hash": "a9e575d272fec21ee0baab114ecd62cb"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\list_run_steps_response.dart", "hash": "f38a5e256061192e62e66d3cee1252d7"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\notifications.dart", "hash": "1ab2ce7d2d7c9d9e510823d8f1982550"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\text_field.dart", "hash": "bc8416181ecb9eb6c88ad70b85f3915b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\combinator\\generated\\sequence_3.dart", "hash": "050f96bbbf01a1f86e208d7d8cc08901"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\text.dart", "hash": "c1fa7198cafde989fd84c9c03ebddeb9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\transformers\\backpressure\\sample.dart", "hash": "98ad95f9d48fa93a9cdc4a8fa0f69c73"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\src\\_internal\\reference_failure.dart", "hash": "ace753cedb5cd81ec4ee468c2d887285"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\oes_texture_half_float.dart", "hash": "a8b21e7f9e07675ace0ab0adfb3a9f99"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\core\\runnable\\function.dart", "hash": "0b0f48468e3b90b8de4ba9a69876be6b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\retrievers\\fake.dart", "hash": "1c296bec8146c630895b806a6f5305a3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_it-7.7.0\\LICENSE", "hash": "2ca873395712084d52af72aa1d4a2b80"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\fido.dart", "hash": "f9c1699509f8a9a0ebb70f224f99cf55"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart", "hash": "e3737de39b4843c820044597cd5f3b5f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics.dart", "hash": "8bfe2204828f5221cc7a3fcb47e5a64f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart", "hash": "6f50583c3f17a5209d18ed90e5c521b9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\async_memoizer.dart", "hash": "abcb2d6facc18b2af070cb86cbb1c764"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\map.dart", "hash": "822f0a79dfd6a3c997d2b898ec420b97"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\animation\\tween.dart", "hash": "deb256229ac9a60b1f5dee744af2411c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf-1.4.2\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\search_anchor.dart", "hash": "39b8956cc778a8292133c1be197d73fc"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\color_scheme.dart", "hash": "473a3ebe500198a12ebbc76b84ae9784"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\widgets\\view.dart", "hash": "9601fa00a20190d932ac3e402cbd243c"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\web\\icons\\Icon-maskable-192.png", "hash": "c457ef57daa1d16f64b27b786ec2ea3c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rfc_6901-0.2.0\\lib\\src\\_internal\\new_element.dart", "hash": "da0eccd3bc2b19edb28b6a30de20a5ff"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\expression\\result.dart", "hash": "bc503b6c5e3658a13efaee4e0638935a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\lib\\src\\tree_base.dart", "hash": "61b8716847e9a3ca1bff526d7603b9a8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\gestures\\binding.dart", "hash": "6a314e7c16349ce1a46736abaa73df76"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\src\\typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\matcher\\matches\\matches_iterator.dart", "hash": "4c92351d347c52a00797317aa487600f"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\gestures.dart", "hash": "7b6199dff5808c0464b865fe03c4c616"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\misc\\label.dart", "hash": "7de7aec8bf9b53488692403a3feb7672"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\service_workers.dart", "hash": "74202a148c536b1b659ab009beb77d23"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\lib\\src\\json_value.dart", "hash": "002be4c072c0cc5c5e72b5ff6d0c490b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\result.dart", "hash": "c6e362e3e6b16241c22db67cbbd6b85b"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\.dart_tool\\package_config_subset", "hash": "1552ed9a4eb7c709e0736fae1689e4d8"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.12.0\\lib\\src\\result\\capture_transformer.dart", "hash": "e82a9b67ba33ae635b9b083ef147fb9b"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\memory\\chat_message_history\\chat_message_history.dart", "hash": "8855174b105010e62a19bf684db0cbf4"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webgl_color_buffer_float.dart", "hash": "784fc2946fba67fc31c328cbfbbf71a8"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\scheduler\\binding.dart", "hash": "c4f7bb297ee43a5e36a7c6e2e3b2d05c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\documents\\loaders\\loaders.dart", "hash": "06163b1069775f55c6956d84ec0a7c35"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\webidl.dart", "hash": "e277cd24cc460f69f51b0256a4f283ce"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\llms\\llms.dart", "hash": "99279d8e3aa087cc38276b934dd278de"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_rx\\src\\rx_types\\rx_core\\rx_num.dart", "hash": "681651b9dc5965962173e941aefd0625"}, {"path": "D:\\project\\vs code\\novel_app002\\novel_app\\lib\\models\\chapter.g.dart", "hash": "e4f623adf63ec27dd53bda651f53d942"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\lib\\html_escape.dart", "hash": "efc823416c4e5e4dcced4cc2c3bbd89c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart", "hash": "10aa125a810b9da9e2c9127c1b52e8bf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_path-0.6.6\\lib\\src\\json_path.dart", "hash": "dbc605a79f3981f77620b612a48cbfaa"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\rxdart-0.28.0\\lib\\src\\streams\\fork_join.dart", "hash": "848f74750b2ecf581969c7a0dd4c2c36"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\repeating.dart", "hash": "282aa0046bbbfcbc30050f7fab282778"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\painting\\_network_image_web.dart", "hash": "45310570b68f9ae3cbfa6fb09be6b6ad"}, {"path": "build\\web\\manifest.json", "hash": "f448fbac2eb30773e5cabf3d01392d1c"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\beautiful_soup_dart-0.3.0\\lib\\src\\bs4_element.dart", "hash": "68f5ffde11f52744446babe44de300d5"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\action\\cast.dart", "hash": "dc379ed249557649f50b9c27d0033be6"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_navigation\\src\\root\\get_material_app.dart", "hash": "3f90a55a48564efb5a9ca486434751e0"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart", "hash": "16ff0bd0c4c1f269ee8a2ed5a1707c37"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\hive-2.2.3\\lib\\src\\hive_impl.dart", "hash": "17d6409e5c71813bb1715f370eca420a"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\lib\\get_core\\src\\get_main.dart", "hash": "da568acf3a4b2e7a7ee1273e6349d474"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\model_io\\prompts\\base_prompt.dart", "hash": "9b93b66ad89aac15f5252fed4ea95adf"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\chip.dart", "hash": "3f81fa08649642e62d1d33e1d0d400b1"}, {"path": "D:\\element\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart", "hash": "d913b7ad1ca5b07927bdce321f5d0222"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\langchain-0.3.1+1\\lib\\src\\chains\\sequential.dart", "hash": "c3238d1f5ea77839f40a150c61524bec"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\openai_dart-0.1.7\\lib\\src\\generated\\schema\\run_step_object.dart", "hash": "b429ae2d2e30a36bb6beee6001cff097"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\lib\\src\\dom\\encoding.dart", "hash": "0fae4441d0dbf3ea08446e7036a88ddf"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\lib\\src\\default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\lib\\src\\parser\\repeater\\character.dart", "hash": "f6f8ad33193db66deb89d68e406eeaf9"}, {"path": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_info_plus-4.2.0\\LICENSE", "hash": "93a5f7c47732566fb2849f7dcddabeaf"}]}