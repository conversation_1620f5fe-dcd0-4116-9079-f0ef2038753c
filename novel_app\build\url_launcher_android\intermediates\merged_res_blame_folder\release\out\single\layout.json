[{"merged": "io.flutter.plugins.urllauncher.url_launcher_android-merged_res-26:/layout/custom_dialog.xml", "source": "io.flutter.plugins.urllauncher.url_launcher_android-core-1.13.1-4:/layout/custom_dialog.xml"}, {"merged": "io.flutter.plugins.urllauncher.url_launcher_android-merged_res-26:/layout/ime_secondary_split_test_activity.xml", "source": "io.flutter.plugins.urllauncher.url_launcher_android-core-1.13.1-4:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "io.flutter.plugins.urllauncher.url_launcher_android-merged_res-26:/layout/notification_template_part_chronometer.xml", "source": "io.flutter.plugins.urllauncher.url_launcher_android-core-1.13.1-4:/layout/notification_template_part_chronometer.xml"}, {"merged": "io.flutter.plugins.urllauncher.url_launcher_android-merged_res-26:/layout/browser_actions_context_menu_page.xml", "source": "io.flutter.plugins.urllauncher.url_launcher_android-browser-1.8.0-10:/layout/browser_actions_context_menu_page.xml"}, {"merged": "io.flutter.plugins.urllauncher.url_launcher_android-merged_res-26:/layout/notification_template_part_time.xml", "source": "io.flutter.plugins.urllauncher.url_launcher_android-core-1.13.1-4:/layout/notification_template_part_time.xml"}, {"merged": "io.flutter.plugins.urllauncher.url_launcher_android-merged_res-26:/layout/browser_actions_context_menu_row.xml", "source": "io.flutter.plugins.urllauncher.url_launcher_android-browser-1.8.0-10:/layout/browser_actions_context_menu_row.xml"}, {"merged": "io.flutter.plugins.urllauncher.url_launcher_android-merged_res-26:/layout/ime_base_split_test_activity.xml", "source": "io.flutter.plugins.urllauncher.url_launcher_android-core-1.13.1-4:/layout/ime_base_split_test_activity.xml"}]