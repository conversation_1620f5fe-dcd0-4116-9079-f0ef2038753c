<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="AI小说生成器 - 使用AI技术生成精彩小说">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self' https://*.dznovel.top https://*.openai.com https://*.aliyuncs.com https://*.googleapis.com https://*.baidubce.com https://*.volces.com https://*.siliconflow.cn https://*.deepseek.com https://www.gstatic.com *; worker-src 'self' blob:;">
  <meta http-equiv="Access-Control-Allow-Origin" content="*">
  <meta http-equiv="Access-Control-Allow-Methods" content="GET,POST,PUT,DELETE,OPTIONS">
  <meta http-equiv="Access-Control-Allow-Headers" content="Origin, X-Requested-With, Content-Type, Accept">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="AI小说生成器">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>AI小说生成器</title>
  <link rel="manifest" href="manifest.json">

  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = '{{flutter_service_worker_version}}';
  </script>

  <!-- CanvasKit配置 -->
  <script>
    window.flutterConfiguration = {
      canvasKitBaseUrl: "/canvaskit/"
    };
  </script>

  <style>
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      -ms-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
    }

    .loader {
      border: 8px solid #f3f3f3;
      border-radius: 50%;
      border-top: 8px solid #2196F3;
      width: 60px;
      height: 60px;
      -webkit-animation: spin 1s linear infinite;
      animation: spin 1s linear infinite;
    }

    @-webkit-keyframes spin {
      0% { -webkit-transform: rotate(0deg); }
      100% { -webkit-transform: rotate(360deg); }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="loading">
    <div class="loader"></div>
  </div>

  <script src="flutter.js" defer></script>
  <script>
    window.addEventListener('load', function() {
      var loading = document.querySelector('.loading');
      var maxAttempts = 5;
      var attempts = 0;

      function initializeApp() {
        attempts++;
        if (typeof window.flutter !== 'undefined') {
          window.flutter.loader.load({
            serviceWorker: {
              serviceWorkerVersion: serviceWorkerVersion,
            }
          }).then(function(appRunner) {
            loading.classList.add('fade');
          }).catch(function(error) {
            console.error('Flutter initialization error:', error);
            showError('Flutter初始化失败: ' + error);
          });
        } else if (attempts < maxAttempts) {
          console.log('等待Flutter初始化，尝试 ' + attempts + '/' + maxAttempts);
          setTimeout(initializeApp, 1000);
        } else {
          showError('无法加载Flutter应用');
        }
      }

      function showError(message) {
        loading.innerHTML = 
          '<div style="text-align: center; font-family: sans-serif; color: #721c24; background-color: #f8d7da; padding: 20px; border-radius: 5px; max-width: 500px;">' +
          '<h3>加载应用程序时出错</h3>' +
          '<p>' + message + '</p>' +
          '<p>请尝试刷新页面或使用其他浏览器。</p>' +
          '<button onclick="location.reload()" style="padding: 10px 20px; background: #0275d8; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 15px;">刷新页面</button>' +
          '</div>';
      }

      // 延迟一点时间再初始化，确保flutter.js已加载
      setTimeout(initializeApp, 100);
    });
  </script>
</body>
</html>
