["D:\\project\\vs code\\novel_app002\\novel_app\\build\\web\\*\\index.html", "D:\\project\\vs code\\novel_app002\\novel_app\\build\\web\\flutter_bootstrap.js", "D:\\project\\vs code\\novel_app002\\novel_app\\build\\web\\main.dart.js", "build\\web\\assets\\assets/images/coffee_qrcode.svg", "build\\web\\assets\\assets/images/wechat_pay.png.jpg", "build\\web\\assets\\assets/version.json", "build\\web\\assets\\assets/fonts/NotoSerifSC-Regular.otf", "build\\web\\assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "build\\web\\assets\\fonts/MaterialIcons-Regular.otf", "build\\web\\assets\\shaders/ink_sparkle.frag", "build\\web\\assets\\AssetManifest.json", "build\\web\\assets\\AssetManifest.bin", "build\\web\\assets\\AssetManifest.bin.json", "build\\web\\assets\\FontManifest.json", "build\\web\\assets\\NOTICES", "build\\web\\.htaccess", "build\\web\\build\\web\\assets\\AssetManifest.bin.json", "build\\web\\build\\web\\assets\\FontManifest.json", "build\\web\\build\\web\\icons\\Icon-192.png", "build\\web\\DEPLOYMENT.md", "build\\web\\download_canvaskit.py", "build\\web\\favicon.png", "build\\web\\flutter.js", "build\\web\\flutter_backup.js", "build\\web\\flutter_canvaskit_config.js", "build\\web\\icons\\Icon-192.png", "build\\web\\icons\\Icon-512.png", "build\\web\\icons\\Icon-maskable-192.png", "build\\web\\icons\\Icon-maskable-512.png", "build\\web\\index.html.new", "build\\web\\manifest.json", "build\\web\\nginx.conf.example", "build\\web\\test.html", "build\\web\\web.config", "build\\web\\welcome.html", "build\\web\\welcome_simple.html", "build\\web\\flutter_service_worker.js"]