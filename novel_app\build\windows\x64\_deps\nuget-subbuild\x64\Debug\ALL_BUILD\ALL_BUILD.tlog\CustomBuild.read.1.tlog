^D:\PROJECT\VS CODE\NOVEL_APP002\NOVEL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKELISTS.TXT
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\CMAKEGENERICSYSTEM.CMAKE
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\CMAKEINITIALIZECONFIGS.CMAKE
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\CMAKE<PERSON>Y<PERSON>EMSPECIFICINFORMATION.CMAKE
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\CMAKESYSTEMSPECIFICINITIALIZE.CMAKE
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\EXTERNALPROJECT.CMAKE
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\EXTERNALPROJECT\PATCHINFO.TXT.IN
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\EXTERNALPROJECT\REPOSITORYINFO.TXT.IN
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\EXTERNALPROJECT\UPDATEINFO.TXT.IN
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\EXTERNALPROJECT\CFGCMD.TXT.IN
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\EXTERNALPROJECT\DOWNLOAD.CMAKE.IN
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\EXTERNALPROJECT\MKDIRS.CMAKE.IN
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\EXTERNALPROJECT\SHARED_INTERNAL_COMMANDS.CMAKE
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\PLATFORM\WINDOWS-INITIALIZE.CMAKE
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\PLATFORM\WINDOWS.CMAKE
C:\PROGRAM FILES\MICROSOFT VISUAL STUDIO\2022\COMMUNITY\COMMON7\IDE\COMMONEXTENSIONS\MICROSOFT\CMAKE\CMAKE\SHARE\CMAKE-3.30\MODULES\PLATFORM\WINDOWSPATHS.CMAKE
D:\PROJECT\VS CODE\NOVEL_APP002\NOVEL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\3.30.5-MSVC23\CMAKESYSTEM.CMAKE
D:\PROJECT\VS CODE\NOVEL_APP002\NOVEL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\NUGET-POPULATE-PREFIX\TMP\NUGET-POPULATE-MKDIRS.CMAKE
