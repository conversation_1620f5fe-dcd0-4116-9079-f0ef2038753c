import 'package:get/get.dart';
import 'package:novel_app/services/ai_service.dart';
// 以下导入的文件已被删除，此备份文件仅作参考
// import 'package:novel_app/prompts/master_prompts.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/services/cache_service.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'dart:convert';
import 'dart:async';
import 'package:uuid/uuid.dart';
import 'dart:math';
import 'package:novel_app/services/langchain_integration_service.dart';
import 'package:novel_app/services/novel_generator_outline.dart';
import 'package:novel_app/services/novel_generator_chapter.dart';
import 'package:novel_app/services/novel_content_checker.dart';
import 'package:novel_app/utils/logger.dart';

// LangChain风格的消息类
class ChatMessage {
  final String role; // system, user, assistant
  final String content;
  final DateTime timestamp;

  ChatMessage({
    required this.role,
    required this.content,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() => {
        'role': role,
        'content': content,
        'timestamp': timestamp.toIso8601String(),
      };

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      role: json['role'],
      content: json['content'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

// 聊天历史管理类
class ChatHistory {
  final String sessionId;
  final List<ChatMessage> messages;
  final int maxContextLength;
  final int maxMessages;

  ChatHistory({
    String? sessionId,
    List<ChatMessage>? messages,
    this.maxContextLength = 16000000,
    this.maxMessages = 50,
  })  : sessionId = sessionId ?? const Uuid().v4(),
        messages = messages ?? [];

  // 添加消息
  void addMessage(ChatMessage message) {
    messages.add(message);
    // 如果消息太多，移除最旧的消息
    if (messages.length > maxMessages) {
      messages.removeAt(0);
    }
  }

  // 获取格式化的上下文
  String getFormattedContext() {
    int totalLength = 0;
    final formattedMessages = <String>[];

    // 从最新的消息开始，直到填满上下文窗口
    for (int i = messages.length - 1; i >= 0; i--) {
      final message = messages[i];
      final formattedMessage = '${message.role}: ${message.content}';

      totalLength += formattedMessage.length;
      if (totalLength > maxContextLength) {
        break;
      }

      formattedMessages.insert(0, formattedMessage);
    }

    return formattedMessages.join('\n\n');
  }

  // 获取原始消息列表用于API调用
  List<Map<String, String>> getAPIMessages() {
    return messages
        .map((msg) => {
              'role': msg.role,
              'content': msg.content,
            })
        .toList();
  }

  // 清空历史
  void clear() {
    messages.clear();
  }

  // 转换为JSON
  Map<String, dynamic> toJson() => {
        'sessionId': sessionId,
        'messages': messages.map((m) => m.toJson()).toList(),
        'maxContextLength': maxContextLength,
        'maxMessages': maxMessages,
      };

  // 从JSON创建
  factory ChatHistory.fromJson(Map<String, dynamic> json) {
    return ChatHistory(
      sessionId: json['sessionId'],
      messages: (json['messages'] as List)
          .map((m) => ChatMessage.fromJson(m))
          .toList(),
      maxContextLength: json['maxContextLength'] ?? 16000000,
      maxMessages: json['maxMessages'] ?? 50,
    );
  }
}

// 小说背景信息类
class NovelContext {
  final String title;
  final String genre;
  final String plotOutline;
  final Map<String, dynamic> characters;
  final Map<String, dynamic> worldBuilding;
  final String style;
  final String tone;

  NovelContext({
    required this.title,
    required this.genre,
    required this.plotOutline,
    this.characters = const {},
    this.worldBuilding = const {},
    this.style = '',
    this.tone = '',
  });

  String getFormattedContext() {
    final buffer = StringBuffer();

    buffer.writeln('# 小说信息');
    buffer.writeln('标题: $title');
    buffer.writeln('类型: $genre');

    if (style.isNotEmpty) {
      buffer.writeln('风格: $style');
    }

    if (tone.isNotEmpty) {
      buffer.writeln('基调: $tone');
    }

    buffer.writeln('\n# 情节大纲');
    buffer.writeln(plotOutline);

    if (characters.isNotEmpty) {
      buffer.writeln('\n# 角色信息');
      characters.forEach((name, info) {
        buffer.writeln('- $name: ${json.encode(info)}');
      });
    }

    if (worldBuilding.isNotEmpty) {
      buffer.writeln('\n# 世界观设定');
      worldBuilding.forEach((aspect, detail) {
        buffer.writeln('- $aspect: $detail');
      });
    }

    return buffer.toString();
  }

  Map<String, dynamic> toJson() => {
        'title': title,
        'genre': genre,
        'plotOutline': plotOutline,
        'characters': characters,
        'worldBuilding': worldBuilding,
        'style': style,
        'tone': tone,
      };

  factory NovelContext.fromJson(Map<String, dynamic> json) {
    return NovelContext(
      title: json['title'],
      genre: json['genre'],
      plotOutline: json['plotOutline'],
      characters: json['characters'] ?? {},
      worldBuilding: json['worldBuilding'] ?? {},
      style: json['style'] ?? '',
      tone: json['tone'] ?? '',
    );
  }
}

/// LangChain驱动的小说生成服务
/// 使用LangChain管理上下文窗口和会话状态
class LangchainNovelGeneratorService extends GetxService {
  final AIService _aiService;
  final ApiConfigController _apiConfig;
  final CacheService _cacheService;
  final LangchainIntegrationService _langchainService;
  final NovelGeneratorOutlineService _outlineService;
  final NovelGeneratorChapterService _chapterService;
  final NovelContentCheckerService _contentCheckerService;

  // 会话管理
  final Map<String, ChatHistory> _sessions = {};
  final Map<String, NovelContext> _sessionContexts = {};
  final Map<String, String> _novelContent = {}; // 添加小说内容缓存
  final Map<String, Novel> _novels = {};

  // 响应式状态
  final RxMap<String, bool> _generatingStatus = <String, bool>{}.obs;
  final RxMap<String, String> _lastError = <String, String>{}.obs;

  LangchainNovelGeneratorService(
    this._aiService,
    this._cacheService,
    this._apiConfig,
    this._langchainService,
    this._outlineService,
    this._chapterService,
    this._contentCheckerService,
  );

  // 清除所有数据
  void clearAllData() {
    _sessions.clear();
    _sessionContexts.clear();
    _novelContent.clear();
    _novels.clear();
    _langchainService.clearAllSessionHistories();
    AppLogger.info("LangchainNovelGeneratorService: 已清除所有数据");
  }

  // 创建新的小说会话
  Future<String> createNovelSession({
    required String title,
    required String genre,
    String? theme,
    String? targetReaders,
    String? outline,
  }) async {
    try {
      // 如果未提供大纲，则生成一个
      String finalOutline = outline ??
          await _outlineService.generateOutline(
            title: title,
            genre: genre,
            theme: theme ?? '未指定',
            targetReaders: targetReaders,
          );

      // 创建LangChain会话
      final sessionId = await _langchainService.createNovelSession(
        title: title,
        genre: genre,
        outline: finalOutline,
        theme: theme,
        targetReaders: targetReaders,
      );

      // 创建小说对象
      final novel = Novel(
        id: sessionId,
        title: title,
        author: 'AI与用户',
        coverImagePath: '',
        genre: genre,
        theme: theme ?? '未指定',
        targetReaders: targetReaders,
        outline: finalOutline,
        chapters: [],
        currentChapterIndex: 0,
        isCompleted: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 存储小说
      _novels[sessionId] = novel;

      AppLogger.info('创建了新的小说会话: $sessionId');
      return sessionId;
    } catch (e) {
      AppLogger.error('创建小说会话失败: $e');
      rethrow;
    }
  }

  // 获取会话列表
  List<Map<String, dynamic>> getNovelSessions() {
    return _sessions.entries.map((entry) {
      final context = _sessionContexts[entry.key];
      return {
        'sessionId': entry.key,
        'title': context?.title ?? '未命名小说',
        'genre': context?.genre ?? '未知类型',
        'messageCount': entry.value.messages.length,
        'lastModified': entry.value.messages.isNotEmpty
            ? entry.value.messages.last.timestamp
            : DateTime.now(),
      };
    }).toList();
  }

  // 获取特定会话
  ChatHistory? getSession(String sessionId) {
    return _sessions[sessionId];
  }

  // 获取特定会话的上下文
  NovelContext? getSessionContext(String sessionId) {
    return _sessionContexts[sessionId];
  }

  // 删除会话
  Future<void> deleteSession(String sessionId) async {
    _sessions.remove(sessionId);
    _sessionContexts.remove(sessionId);
    _generatingStatus.remove(sessionId);
    _lastError.remove(sessionId);
    _novels.remove(sessionId);

    await saveSessions();
  }

  // 生成小说章节
  Future<String> generateChapter({
    required String sessionId,
    required int chapterNumber,
    void Function(String)? onProgress,
    void Function(String)? onContent,
  }) async {
    if (!_novels.containsKey(sessionId)) {
      throw Exception('小说会话不存在: $sessionId');
    }

    final novel = _novels[sessionId]!;

    try {
      onProgress?.call('准备生成第$chapterNumber章...');

      // 从小说对象中获取必要信息
      final title = novel.title;
      final genre = novel.genre;
      final theme = novel.theme;
      final outline = novel.outline;
      final totalChapters = _estimateTotalChapters(outline);

      // 获取前面的章节作为上下文
      final previousChapters =
          novel.chapters.where((ch) => ch.number < chapterNumber).toList();

      // 构建章节提示词
      final chapterPrompt = '''
请为《$title》创作第$chapterNumber章。

【参考大纲】
${_extractChapterOutlineFromFull(outline, chapterNumber)}

【创作要求】
- 紧密遵循大纲内容
- 风格符合"$genre"类型
- 主题围绕"$theme"展开
- 字数控制在3000-4000字之间
- 直接开始创作，不要添加章节标题或其他标记
''';

      onProgress?.call('正在使用LangChain生成第$chapterNumber章...');

      // 使用LangChain生成章节内容
      final buffer = StringBuffer();

      await for (final chunk in await _langchainService.generateNovelStream(
        sessionId: sessionId,
        prompt: chapterPrompt,
        temperature: 0.75,
        topP: 0.92,
        maxTokens: 4000,
      )) {
        buffer.write(chunk);
        if (onContent != null) {
          onContent(chunk);
        }
      }

      // 清理内容
      String generatedContent = _cleanContent(buffer.toString());

      // 检查内容质量
      onProgress?.call('正在评估章节质量...');
      final evaluation =
          await _contentCheckerService.evaluateChapterQuality(generatedContent);

      // 如果质量评分较低，尝试改进内容
      if (evaluation.containsKey('overall_score') &&
          evaluation['overall_score'] is num &&
          evaluation['overall_score'] < 6.5) {
        onProgress?.call('章节质量评分较低: ${evaluation['overall_score']}，尝试改进内容...');

        // 提取当前章节的大纲
        final chapterOutline =
            _extractChapterOutlineFromFull(outline, chapterNumber);

        // 改进内容
        generatedContent = await _contentCheckerService.improveChapterContent(
          chapterContent: generatedContent,
          outline: chapterOutline,
          evaluation: evaluation,
          onProgress: onProgress,
        );
      }

      // 创建章节对象
      final chapter = Chapter(
        id: '${sessionId}_chapter_$chapterNumber',
        number: chapterNumber,
        title: _generateChapterTitle(outline, chapterNumber),
        content: generatedContent,
        wordCount: _countWords(generatedContent),
        createdAt: DateTime.now(),
      );

      // 保存章节
      final existingChapterIndex =
          novel.chapters.indexWhere((ch) => ch.number == chapterNumber);
      if (existingChapterIndex >= 0) {
        // 更新现有章节
        novel.chapters[existingChapterIndex] = chapter;
      } else {
        // 添加新章节
        novel.chapters.add(chapter);
        novel.chapters.sort((a, b) => a.number.compareTo(b.number));
      }

      // 更新小说对象
      novel.currentChapterIndex = chapterNumber;
      novel.updatedAt = DateTime.now();

      // 检查小说是否已完成
      if (chapterNumber >= totalChapters) {
        novel.isCompleted = true;
      }

      onProgress?.call('第$chapterNumber章生成完成');
      return generatedContent;
    } catch (e) {
      AppLogger.error('生成章节失败: $e');
      throw Exception('生成章节失败: $e');
    }
  }

  // 获取小说信息
  Novel? getNovel(String sessionId) {
    return _novels[sessionId];
  }

  // 获取所有小说
  List<Novel> getAllNovels() {
    return _novels.values.toList();
  }

  // 从大纲中提取章节标题
  String _generateChapterTitle(String outline, int chapterNumber) {
    // 尝试从大纲中提取章节标题
    final chapterInfo = _extractChapterOutlineFromFull(outline, chapterNumber);

    // 检查是否包含标题行
    final lines = chapterInfo.split('\n');
    for (final line in lines) {
      final trimmed = line.trim();
      // 查找可能的标题行
      if (trimmed.startsWith('第$chapterNumber章') ||
          trimmed.startsWith('章节$chapterNumber') ||
          trimmed.contains('标题：')) {
        // 提取冒号或空格后的内容作为标题
        final titleMatch = RegExp(r'[：:]\s*(.+)$').firstMatch(trimmed);
        if (titleMatch != null && titleMatch.group(1) != null) {
          return titleMatch.group(1)!.trim();
        } else if (trimmed.contains(' ')) {
          // 空格分隔的标题
          final parts = trimmed.split(' ');
          if (parts.length > 1) {
            return parts.sublist(1).join(' ').trim();
          }
        }
      }
    }

    // 默认标题
    return '第$chapterNumber章';
  }

  // 从完整大纲中提取特定章节的大纲
  String _extractChapterOutlineFromFull(String fullOutline, int chapterNumber) {
    try {
      // 尝试匹配第N章的内容
      final RegExp chapterRegExp = RegExp(
          r'第' + chapterNumber.toString() + r'章[^]*?(?=第\d+章|$)',
          dotAll: true);

      final match = chapterRegExp.firstMatch(fullOutline);
      if (match != null) {
        return match.group(0)!.trim();
      }

      // 备用方法：查找包含章节号的行和其后的内容
      final lines = fullOutline.split('\n');
      int startLine = -1;

      for (int i = 0; i < lines.length; i++) {
        if (lines[i].contains('第$chapterNumber章') ||
            lines[i].contains('章节$chapterNumber：') ||
            lines[i].contains('$chapterNumber.') ||
            lines[i].contains('$chapterNumber、')) {
          startLine = i;
          break;
        }
      }

      if (startLine >= 0) {
        int endLine = lines.length;

        // 寻找下一章的开始
        for (int i = startLine + 1; i < lines.length; i++) {
          if (lines[i].contains('第${chapterNumber + 1}章') ||
              lines[i].contains('章节${chapterNumber + 1}：') ||
              lines[i].contains('${chapterNumber + 1}.') ||
              lines[i].contains('${chapterNumber + 1}、')) {
            endLine = i;
            break;
          }
        }

        return lines.sublist(startLine, endLine).join('\n').trim();
      }

      // 如果无法提取具体章节，返回部分大纲
      return '无法找到第$chapterNumber章的具体大纲。以下是总体大纲：\n\n${fullOutline.length > 500 ? '${fullOutline.substring(0, 500)}...(省略)' : fullOutline}';
    } catch (e) {
      AppLogger.error('提取章节大纲失败: $e');
      return '提取章节大纲时出错。请参考总体大纲生成第$chapterNumber章。';
    }
  }

  // 估算总章节数
  int _estimateTotalChapters(String outline) {
    try {
      // 从大纲中查找所有章节标记
      final chapterMatches = RegExp(r'第(\d+)章').allMatches(outline).toList();

      if (chapterMatches.isNotEmpty) {
        // 提取最后一章的编号
        final lastChapterMatch = chapterMatches.last;
        final lastChapterNumber = int.parse(lastChapterMatch.group(1)!);
        return lastChapterNumber;
      }

      // 如果找不到明确的章节标记，根据大纲长度估算
      final estimatedLength = outline.length ~/ 100;
      return estimatedLength.clamp(5, 30); // 保持在5-30章之间
    } catch (e) {
      // 默认返回10章
      return 10;
    }
  }

  // 清理生成的内容
  String _cleanContent(String content) {
    // 移除常见的标记和格式
    String cleaned = content;

    // 移除可能出现的章节标题
    cleaned = cleaned.replaceAll(RegExp(r'^第\d+章.*?\n', multiLine: true), '');

    // 移除特殊标记
    final marksToRemove = [
      '【章节开始】',
      '【章节结束】',
      '【正文开始】',
      '【正文结束】',
      '以下是',
      '以上是',
      '本章内容',
      '章节内容',
      '内容如下',
      '```',
      '---'
    ];

    for (final mark in marksToRemove) {
      cleaned = cleaned.replaceAll(mark, '');
    }

    // 处理段落和空行
    final paragraphs = cleaned
        .split('\n')
        .map((line) => line.trim())
        .where((line) => line.isNotEmpty)
        .toList();

    // 确保段落之间有空行
    return paragraphs.join('\n\n');
  }

  // 统计文本中的字数
  int _countWords(String text) {
    // 中文计数：每个字符计为一个词
    final chineseCount = RegExp(r'[\u4e00-\u9fa5]').allMatches(text).length;

    // 英文计数：按空格分隔
    final englishWords = text
        .replaceAll(RegExp(r'[\u4e00-\u9fa5]'), '')
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .length;

    return chineseCount + englishWords;
  }

  // 加载所有会话
  Future<void> loadChatSessions() async {
    try {
      _sessions.clear();
      _sessionContexts.clear();
      _novelContent.clear();

      final sessionsJson = _cacheService.getNovelSessions();
      if (sessionsJson != null) {
        final Map<String, dynamic> sessionsData = jsonDecode(sessionsJson);

        sessionsData.forEach((sessionId, sessionData) {
          final history = ChatHistory.fromJson(sessionData['history']);
          _sessions[sessionId] = history;

          // 恢复上下文
          if (sessionData.containsKey('context')) {
            _sessionContexts[sessionId] =
                NovelContext.fromJson(sessionData['context']);
          }

          // 恢复小说内容
          if (sessionData.containsKey('novelContent')) {
            _novelContent[sessionId] = sessionData['novelContent'];
          }
        });
      }
    } catch (e) {
      print('加载会话失败: $e');
      // 出错时不处理，使用空会话列表
    }
  }

  // 保存所有会话
  Future<void> saveSessions() async {
    try {
      final Map<String, dynamic> sessionsData = {};

      _sessions.forEach((sessionId, history) {
        final context = _sessionContexts[sessionId];
        sessionsData[sessionId] = {
          'history': history.toJson(),
          if (context != null) 'context': context.toJson(),
          if (_novelContent.containsKey(sessionId))
            'novelContent': _novelContent[sessionId],
        };
      });

      await _cacheService.saveNovelSessions(jsonEncode(sessionsData));
    } catch (e) {
      print('保存会话失败: $e');
      // 出错时不处理，继续使用内存中的会话
    }
  }

  // 检查会话是否正在生成
  bool isGenerating(String sessionId) {
    return _generatingStatus[sessionId] ?? false;
  }

  // 获取上次错误
  String? getLastError(String sessionId) {
    return _lastError[sessionId];
  }

  // 构建系统提示词
  String buildSystemPrompt(NovelContext context, [String? novelContent]) {
    final buffer = StringBuffer();

    buffer.writeln('''你是一位创意丰富、叙事能力极强的AI小说家助手，专注于帮助用户创作引人入胜的故事。

# 小说创作背景
${context.getFormattedContext()}''');

    // 添加小说内容作为上下文
    if (novelContent != null && novelContent.isNotEmpty) {
      buffer.writeln('\n# 小说已有内容（请确保回答与此保持一致）');

      // 截断过长的内容，避免超出模型上下文窗口
      const maxContentLength = 50; // 可根据模型上下文窗口大小调整
      if (novelContent.length > maxContentLength) {
        final truncatedContent =
            novelContent.substring(novelContent.length - maxContentLength);
        buffer.writeln('$truncatedContent...');
        buffer.writeln('\n(注：这里只显示了小说的最后部分内容，请确保回答与整体故事保持一致)');
        print(
            '小说内容过长，已截断。原长度: ${novelContent.length}字符，截断后: ${truncatedContent.length}字符');
      } else {
        buffer.writeln(novelContent);
        print('添加完整小说内容到系统提示，长度: ${novelContent.length}字符');
      }
    } else {
      print('构建系统提示词：没有小说内容');
    }

    buffer.writeln('''
# 创作指南
1. 你的回答应该保持具有创意和沉浸感的叙事风格
2. 根据提供的小说背景信息和已有内容进行创作，保持情节、风格和人物的一致性
3. 绝对避免重复已有内容中的段落、句子或情节，保持故事的持续推进
4. 不要生硬地解释或总结情节，而是像讲故事一样自然地展开
5. 允许加入创意元素以增强故事体验，但要与已有设定保持一致
6. 回复应该作为小说内容的直接延续，不需要使用引号或前缀
7. 每次续写都要带来新的情节发展，避免故事停滞不前
8. 请认真阅读聊天历史和小说已有内容，保持故事情节的连贯性
9. 当继续故事时，请参考小说的已有内容和之前所有消息中的情节、人物和设定

# 防止重复的特别指南
- 仔细阅读已有内容的最后部分，确保你的续写不会重复之前的内容
- 当你发现自己要写的内容与之前相似时，请转向新的情节发展
- 避免使用重复的句式、词汇和描述方式
- 确保每次续写都能推动故事向前发展，引入新的元素或深化已有元素
- 如果发现对话有循环趋势，请转向新的对话主题或情节发展

# 回应风格
- 始终使用流畅、引人入胜的小说写作风格
- 不要使用机器人式的解释和提示语，直接给出内容
- 不要使用标题和章节编号，除非用户特别要求
- 避免使用"我"或"AI"等暴露你身份的表述，完全沉浸在创作中
- 请直接生成故事内容，不需要写"以下是小说内容"之类的开场白
- 不要回顾或总结已有内容，直接延续故事

现在，请根据用户的输入，继续创作或提供有关这部小说的创意建议。避免重复任何已有内容，确保故事持续向前发展。''');

    final prompt = buffer.toString();
    print('生成系统提示词，长度: ${prompt.length}');
    return prompt;
  }

  // 重置服务
  void reset() {
    _sessions.clear();
    _sessionContexts.clear();
    _generatingStatus.clear();
    _lastError.clear();
  }

  // 保存特定会话
  Future<void> _saveSession(String sessionId) async {
    try {
      await saveSessions(); // 简单起见，保存所有会话
    } catch (e) {
      print('保存会话失败: $e');
    }
  }

  // 更新小说内容
  void updateNovelContent(String sessionId, String novelContent) {
    if (novelContent.isEmpty) {
      print('更新小说内容：内容为空，不更新');
      return;
    }

    _novelContent[sessionId] = novelContent;
    print('更新小说内容：会话ID $sessionId，内容长度: ${novelContent.length}字符');

    // 如果会话已存在，更新系统提示词
    if (_sessions.containsKey(sessionId) &&
        _sessionContexts.containsKey(sessionId)) {
      final history = _sessions[sessionId]!;
      final context = _sessionContexts[sessionId]!;

      // 查找系统消息并更新
      for (int i = 0; i < history.messages.length; i++) {
        if (history.messages[i].role == 'system') {
          history.messages[i] = ChatMessage(
            role: 'system',
            content: buildSystemPrompt(context, novelContent),
          );
          print('已更新系统提示词');
          break;
        }
      }
    } else {
      print('更新小说内容：找不到对应的会话 $sessionId');
    }
  }
}
